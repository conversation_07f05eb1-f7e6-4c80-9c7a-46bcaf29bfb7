"use client";
import { useState } from "react";
import RealtimeMode from "./RealtimeMode";
import PreviewMode from "./PreviewMode";
import SplitMode from "./SplitMode";
import SourceMode from "./SourceMode";
import { DocProvider } from "./editor/doc/DocContext";
import WechatCopyButton from "./WechatCopyButton";

type Mode = "realtime" | "preview" | "split" | "source";

export default function EditorShell() {
  const [mode, setMode] = useState<Mode>("realtime");
  
  return (
    <DocProvider>
      <div className="h-screen flex flex-col">
        <header className="flex items-center gap-4 p-3 border-b">
          {(["realtime", "preview", "split", "source"] as Mode[]).map(m =>
            <button 
              key={m} 
              data-testid={`mode-${m}`} 
              onClick={() => setMode(m)}
              className={`px-3 py-1 rounded ${mode === m ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
            >
              {m}
            </button>
          )}
          <WechatCopyButton />
          <span style={{ marginLeft: "auto", color: "#6b7280" }}>v0.1 • PR-2</span>
        </header>
        <main className="flex-1 overflow-hidden">
          {mode === "realtime" && <RealtimeMode />}
          {mode === "preview" && <PreviewMode />}
          {mode === "split" && <SplitMode />}
          {mode === "source" && <SourceMode />}
        </main>
      </div>
    </DocProvider>
  );
}
