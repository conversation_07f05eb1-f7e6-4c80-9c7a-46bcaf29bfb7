"use client";

import { useRef, useEffect } from "react";
import { MilkdownProvider, Milkdown, useEditor } from "@milkdown/react";
import { Editor, rootCtx, editorViewCtx, defaultValueCtx } from "@milkdown/core";
import { commonmark } from "@milkdown/preset-commonmark";
import { gfm } from "@milkdown/preset-gfm";
import { useDocCtx } from "@/components/editor/doc/DocContext";

export function MilkdownEditor({
  initial = "# 标题\n\n这里开始输入…",
}: { initial?: string }) {
  // 在 Provider 外只做"桥接"与传参
  const { _registerGetter } = useDocCtx();

  return (
    <MilkdownProvider>
      {/* 真正调用 useEditor 的子组件，保证它运行在 Provider 里面 */}
      <EditorInner initial={initial} registerGetter={_registerGetter} />
    </MilkdownProvider>
  );
}

function EditorInner({
  initial, registerGetter,
}: {
  initial: string;
  registerGetter: (fn: () => any | null) => void;
}) {
  const { _emitChange } = useDocCtx();
  // useEditor 必须在 MilkdownProvider 的子树中调用
  const { get } = useEditor(
    (root) =>
      Editor.make()
        .config((ctx) => {
          ctx.set(rootCtx, root);           // 指定挂载容器
          ctx.set(defaultValueCtx, initial); // 初始 Markdown
        })
        .use(commonmark)
        .use(gfm),
    [initial]
  );

  // 注册 getter + 派发"文档变更"事件（合批防抖）
  useEffect(() => {
    const editor = get();
    if (!editor) return;

    // 延迟执行，确保 Milkdown 完全初始化
    const timer = setTimeout(() => {
      editor.action((ctx) => {
        const view = ctx.get(editorViewCtx);

        // 1) 提供统一 getter
        registerGetter(() => view.state.doc.toJSON());

        // 2) 合批触发（150ms）
        let debounceTimer: number | null = null;
        const emit = () => {
          const json = view.state.doc.toJSON();
          _emitChange(json);
        };
        const debouncedEmit = () => {
          if (debounceTimer) window.clearTimeout(debounceTimer);
          debounceTimer = window.setTimeout(emit, 100); // 减少到 100ms
        };

        // 3) 使用 DOM 事件监听而不是包装 dispatchTransaction
        const editorElement = view.dom;
        const handleInput = () => {
          debouncedEmit();
        };

        // 监听多种输入事件
        editorElement.addEventListener('input', handleInput);
        editorElement.addEventListener('keyup', handleInput);
        editorElement.addEventListener('paste', handleInput);

        // 首次同步一次
        emit();

        // 清理函数
        return () => {
          editorElement.removeEventListener('input', handleInput);
          editorElement.removeEventListener('keyup', handleInput);
          editorElement.removeEventListener('paste', handleInput);
          if (debounceTimer) window.clearTimeout(debounceTimer);
        };
      });
    }, 100);

    return () => clearTimeout(timer);
  }, [get, registerGetter, _emitChange]);

  // Milkdown 组件本身不需要 props
  return <Milkdown />;
}
