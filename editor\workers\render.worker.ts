/// <reference lib="webworker" />
import type { RenderReq, RenderRes } from "./protocol";

// 内联渲染函数以避免模块导入问题
const TOKENS = {
  brand:'#16a34a',
  text:'#1f2937',
  heading:'#0f172a',
  link:'#166534',
  codeBg:'#f8fafc',
  codeBorder:'#e5e7eb',
  quoteBar:'#86efac',
  tableBorder:'#d1d5db',
  tableHeaderBg:'#f0fdf4',
  font:{ base:16, h1:28, h2:22, h3:18 },
  line:{ base:1.8, heading:1.4, code:1.6 },
  space:{ p:12, li:8, td:'8px 12px' },
  radius:{ img:'8px', code:'6px' },
} as const;

const css = (o:Record<string,string|number|undefined>) =>
  Object.entries(o).filter(([,v])=>v!==undefined)
    .map(([k,v])=>`${k}:${v}`).join(';');

const esc = (s:string) =>
  s.replace(/[&<>"']/g, c=>({ "&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;" }[c] as string));

const renderParagraph = (inner:string) =>
  `<p style="${css({"font-size":`${TOKENS.font.base}px`,"line-height":TOKENS.line.base,color:TOKENS.text,margin:`0 0 ${TOKENS.space.p}px 0`})}">${inner}</p>`;

const renderHeading = (lvl:1|2|3, inner:string) => {
  const fs = lvl===1?TOKENS.font.h1:lvl===2?TOKENS.font.h2:TOKENS.font.h3;
  return `<h${lvl} style="${css({"font-size":`${fs}px`,"line-height":TOKENS.line.heading,color:TOKENS.heading,margin:"24px 0 12px"})}">${inner}</h${lvl}>`;
};

const renderBlockquote = (inner:string) =>
  `<blockquote style="${css({"border-left":`4px solid ${TOKENS.quoteBar}`,"margin":"12px 0","padding":"6px 12px"})}">${inner}</blockquote>`;

const renderCodeBlock = (codeHtml:string) =>
  `<pre style="${css({background:TOKENS.codeBg,border:`1px solid ${TOKENS.codeBorder}`,"border-radius":TOKENS.radius.code,padding:"12px",overflow:"auto"})}"><code>${codeHtml}</code></pre>`;

const renderImage = (src:string, alt?:string) =>
  `<img src="${esc(src)}" alt="${esc(alt??"")}" style="${css({"max-width":"100%",height:"auto","border-radius":TOKENS.radius.img})}"/>`;

const renderList = (ordered:boolean, items:string[]) =>
  ordered ? `<ol>${items.map(i=>`<li>${i}</li>`).join("")}</ol>`
          : `<ul>${items.map(i=>`<li>${i}</li>`).join("")}</ul>`;

const renderTable = (thead:string, tbody:string) =>
  `<table style="${css({width:"100%","border-collapse":"collapse","table-layout":"fixed",border:`1px solid ${TOKENS.tableBorder}`})}">${thead}${tbody}</table>`;

const ctx: DedicatedWorkerGlobalScope = self as any;

function renderInline(node:any):string{
  return (node.content||[]).map((n:any)=>{
    if(n.type==="text"){
      let t = esc(n.text||"");
      (n.marks||[]).forEach((m:any)=>{
        if(m.type==="strong") t = `<strong>${t}</strong>`;
        if(m.type==="em")     t = `<em>${t}</em>`;
        if(m.type==="code")   t = `<code>${t}</code>`;
        if(m.type==="link")   t = `<a href="${esc(m.attrs?.href||"#")}" title="${esc(m.attrs?.title||"")}">${t}</a>`;
      });
      return t;
    }
    return renderNode(n);
  }).join("");
}

function renderTableHead(node:any){
  const thead = (node.content||[]).find((n:any)=>n.type==="table_header");
  if(!thead) return "";
  const rows = (thead.content||[]).map((tr:any)=>{
    const cells = (tr.content||[]).map((th:any)=>`<th>${renderInline(th)}</th>`).join("");
    return `<tr>${cells}</tr>`;
  }).join("");
  return `<thead>${rows}</thead>`;
}

function renderTableBody(node:any){
  const tbody = (node.content||[]).find((n:any)=>n.type==="table_body");
  if(!tbody) return "";
  const rows = (tbody.content||[]).map((tr:any)=>{
    const cells = (tr.content||[]).map((td:any)=>`<td>${renderInline(td)}</td>`).join("");
    return `<tr>${cells}</tr>`;
  }).join("");
  return `<tbody>${rows}</tbody>`;
}

function renderNode(node:any):string{
  switch(node.type){
    case "paragraph":    return renderParagraph(renderInline(node));
    case "heading":      return renderHeading(Math.min(Math.max(node.attrs?.level||1,1),3) as 1|2|3, renderInline(node));
    case "blockquote":   return renderBlockquote((node.content||[]).map(renderNode).join(""));
    case "code_block":   return renderCodeBlock(esc(node.text || node.content?.[0]?.text || ""));
    case "image":        return renderImage(node.attrs?.src, node.attrs?.alt);
    case "bullet_list":  return renderList(false, (node.content||[]).map(renderNode));
    case "ordered_list": return renderList(true,  (node.content||[]).map(renderNode));
    case "list_item":    return (node.content||[]).map(renderNode).join("");
    case "table":        return renderTable(renderTableHead(node), renderTableBody(node));
    default:             return (node.content||[]).map(renderNode).join("");
  }
}

ctx.onmessage = (e:MessageEvent<RenderReq>) => {
  try{
    if(e.data.type==="render:full"){
      const html = `<article>${(e.data.pmdoc.content||[]).map(renderNode).join("")}</article>`;
      (ctx as any).postMessage({ type:"render:full:ok", html } as RenderRes);
    }
  }catch(err:any){
    (ctx as any).postMessage({ type:"error", message:String(err?.message||err) } as RenderRes);
  }
};
