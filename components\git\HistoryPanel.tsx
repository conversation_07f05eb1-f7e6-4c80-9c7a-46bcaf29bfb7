'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  History,
  GitCommit,
  User,
  Calendar,
  FileText,
  Eye,
  RefreshCw,
  Search,
  GitBranch,
  Clock,
  ChevronDown,
  ChevronRight,
  Database,
  FolderGit2,
  RotateCcw,
  Settings,
  Info,
  ArrowLeftRight,
} from 'lucide-react';
import { GitHubService, GitHubCommit } from '@/lib/github';
import { SimpleGitService, RepoInfo } from '@/lib/simpleGit';
import { RepoSelector } from '@/components/github/RepoSelector';
import { useSession } from 'next-auth/react';

interface HistoryPanelProps {
  onFileSelect?: (filepath: string) => void;
  currentFile?: string | null;
  onCommitContentChange?: (content: string, filename: string) => void;
}

export function HistoryPanel({ onFileSelect, currentFile, onCommitContentChange }: HistoryPanelProps) {
  const { data: session } = useSession();
  const [gitService] = useState(() => SimpleGitService.getInstance());
  const [currentRepo, setCurrentRepo] = useState<RepoInfo | null>(null);
  const [commits, setCommits] = useState<GitHubCommit[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedCommit, setSelectedCommit] = useState<GitHubCommit | null>(null);
  const [commitContent, setCommitContent] = useState<string>('');
  const [commitDetails, setCommitDetails] = useState<any>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [selectedBranch, setSelectedBranch] = useState<string>('');
  const [showFileContent, setShowFileContent] = useState(false);
  const [showRepoSelector, setShowRepoSelector] = useState(false);
  const [branches, setBranches] = useState<string[]>([]);
  const [showBranchDropdown, setShowBranchDropdown] = useState(false);

  useEffect(() => {
    loadRepoInfo();
  }, []);

  useEffect(() => {
    if (currentRepo && session?.accessToken) {
      setSelectedBranch(currentRepo.branch || 'main');
      loadCommits();
      loadBranches();
    }
  }, [currentRepo, session]);

  useEffect(() => {
    if (currentRepo && session?.accessToken && selectedBranch) {
      loadCommits();
    }
  }, [selectedBranch]);

  // 点击外部关闭分支下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showBranchDropdown) {
        const target = event.target as Element;
        if (!target.closest('.branch-dropdown-container')) {
          setShowBranchDropdown(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showBranchDropdown]);

  const loadRepoInfo = () => {
    const repo = gitService.getCurrentRepo();
    setCurrentRepo(repo);
  };

  const loadBranches = async () => {
    console.log('🌿 开始加载分支列表...');
    console.log('🌿 当前仓库:', currentRepo);
    console.log('🌿 访问令牌存在:', !!session?.accessToken);

    if (!currentRepo || !session?.accessToken) {
      console.log('🌿 缺少仓库信息或访问令牌，跳过加载分支');
      return;
    }

    try {
      const githubService = new GitHubService(session.accessToken);
      console.log('🌿 正在从GitHub获取分支列表...');
      const branchList = await githubService.getBranches(currentRepo.owner, currentRepo.repo);
      console.log('🌿 获取到的分支列表:', branchList);
      const branchNames = branchList.map(b => b.name);
      console.log('🌿 分支名称列表:', branchNames);
      setBranches(branchNames);
    } catch (error) {
      console.error('❌ 加载分支列表失败:', error);
      setBranches([selectedBranch || 'main']); // 至少保留当前分支
    }
  };

  const handleBranchChange = (branch: string) => {
    console.log('🌿 切换分支:', branch);
    setSelectedBranch(branch);
    setShowBranchDropdown(false);

    // 更新本地分支信息
    if (currentRepo) {
      const updatedRepo = { ...currentRepo, branch };
      setCurrentRepo(updatedRepo);
    }

    // 清空当前提交数据，重新加载
    setCommits([]);
    setSelectedCommit(null);
    setPage(1);
    setHasMore(true);
  };

  const handleRepoSelected = async (repo: any) => {
    console.log('🔄 选择仓库:', repo);

    // 设置仓库信息到GitService
    const repoInfo: RepoInfo = {
      owner: repo.owner.login,
      repo: repo.name,
      branch: repo.default_branch || 'main'
    };

    // 使用connectRepo方法连接仓库
    await gitService.connectRepo(repoInfo, true); // 跳过示例文件
    setCurrentRepo(repoInfo);
    setSelectedBranch(repoInfo.branch || 'main');

    // 清空之前的数据
    setCommits([]);
    setSelectedCommit(null);
    setPage(1);
    setHasMore(true);

    console.log('✅ 仓库设置完成，开始加载提交历史');
  };



  const loadCommits = async (pageNum = 1, reset = true) => {
    if (!currentRepo || !session?.accessToken) return;

    setLoading(true);
    try {
      const githubService = new GitHubService(session.accessToken);

      // 添加调试信息
      console.log('🔍 获取提交历史:', {
        owner: currentRepo.owner,
        repo: currentRepo.repo,
        branch: selectedBranch,
        page: pageNum
      });

      const newCommits = await githubService.getCommits(
        currentRepo.owner,
        currentRepo.repo,
        pageNum,
        20,
        selectedBranch || undefined
      );

      // 调试返回的提交信息
      console.log('📝 获取到的提交:', newCommits.slice(0, 3).map(c => ({
        sha: c.sha.substring(0, 7),
        message: c.commit.message.split('\n')[0],
        author: c.commit.author.name,
        date: c.commit.author.date
      })));

      if (reset) {
        setCommits(newCommits);
      } else {
        setCommits(prev => [...prev, ...newCommits]);
      }

      setHasMore(newCommits.length === 20);
      setPage(pageNum);
    } catch (error) {
      console.error('加载提交历史失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMoreCommits = () => {
    if (!loading && hasMore) {
      loadCommits(page + 1, false);
    }
  };

  const handleCommitSelect = async (commit: GitHubCommit) => {
    if (!currentRepo || !session?.accessToken) return;

    setSelectedCommit(commit);
    setCommitContent('');
    setCommitDetails(null);

    setLoading(true);
    try {
      const githubService = new GitHubService(session.accessToken);

      // 获取提交详细信息
      const details = await githubService.getCommit(
        currentRepo.owner,
        currentRepo.repo,
        commit.sha
      );
      setCommitDetails(details);

      // 通知父组件显示提交详情（始终显示提交详情，不自动跳转到文件内容）
      if (onCommitContentChange) {
        const commitInfo = {
          commit: details,
          files: details.files || [],
          stats: details.stats
        };
        onCommitContentChange(JSON.stringify(commitInfo), `commit-${commit.sha.substring(0, 7)}.json`);
      }

      // 预加载当前选中文件的内容（但不自动显示）
      if (currentFile) {
        try {
          const content = await githubService.getFileContentAtCommit(
            currentRepo.owner,
            currentRepo.repo,
            currentFile,
            commit.sha
          );
          setCommitContent(content);
        } catch (error) {
          console.error('获取历史文件内容失败:', error);
          setCommitContent('该文件在此提交中不存在或无法访问');
        }
      }
    } catch (error) {
      console.error('获取提交详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleViewCommitFile = () => {
    if (selectedCommit && commitContent && currentFile) {
      setShowFileContent(true);
    }
  };

  const handleRestoreFile = async () => {
    if (!selectedCommit || !commitContent || !currentFile || !currentRepo || !session?.accessToken) {
      return;
    }

    const confirmRestore = confirm(
      `确定要将文件 "${currentFile}" 恢复到提交 ${selectedCommit.sha.substring(0, 7)} 的版本吗？\n\n` +
      `这将覆盖当前文件内容。建议先保存当前更改。`
    );

    if (confirmRestore) {
      try {
        // 更新本地文件内容
        gitService.updateFile(currentFile, commitContent);

        // 如果有文件选择回调，重新选择文件以更新编辑器
        if (onFileSelect) {
          onFileSelect(currentFile);
        }

        alert(`文件已恢复到提交 ${selectedCommit.sha.substring(0, 7)} 的版本`);
      } catch (error) {
        console.error('恢复文件失败:', error);
        alert('恢复文件失败，请重试');
      }
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN', { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const filteredCommits = commits.filter(commit => 
    !searchQuery || 
    commit.commit.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
    commit.commit.author.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (!session?.accessToken) {
    return (
      <div className="p-3 text-center space-y-4">
        <div className="text-sm text-muted-foreground mb-4">
          需要GitHub登录才能查看提交历史
        </div>
        <History className="w-8 h-8 mx-auto text-muted-foreground mb-4" />
        <div className="space-y-2">
          <div className="text-xs text-muted-foreground">
            请先在右上角登录GitHub账户
          </div>
          <div className="text-xs text-muted-foreground border-t border-border pt-2">
            登录后您可以：
          </div>
          <div className="text-xs text-muted-foreground space-y-1">
            <div>• 查看仓库的完整提交历史</div>
            <div>• 浏览文件的历史版本</div>
            <div>• 恢复文件到任意历史版本</div>
          </div>
        </div>
      </div>
    );
  }

  if (!currentRepo) {
    return (
      <div className="p-3 text-center space-y-4">
        <div className="text-sm text-muted-foreground mb-4">
          请先连接GitHub仓库
        </div>
        <History className="w-8 h-8 mx-auto text-muted-foreground mb-4" />
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowRepoSelector(true)}
          className="w-full h-8"
        >
          <ArrowLeftRight className="w-3.5 h-3.5 mr-2" />
          选择仓库
        </Button>
        <div className="text-xs text-muted-foreground space-y-1">
          <div>选择您要查看历史的仓库</div>
          <div>连接后即可浏览提交历史</div>
        </div>

        {/* 仓库选择器 */}
        <RepoSelector
          open={showRepoSelector}
          onOpenChange={setShowRepoSelector}
          onRepoSelected={async (repo) => {
            // 连接到选中的GitHub仓库
            const repoInfo = {
              owner: repo.owner.login,
              repo: repo.name,
              branch: repo.default_branch
            };

            console.log('🔗 连接GitHub仓库到历史面板');
            gitService.connectRepo(repoInfo, true);

            // 自动拉取仓库文件
            if (session?.accessToken) {
              setLoading(true);
              try {
                const githubService = new GitHubService(session.accessToken);
                const files = await githubService.getRepoContents(repo.owner.login, repo.name);

                if (Array.isArray(files)) {
                  gitService.clearFiles();
                  const pulledFiles = new Map<string, string>();

                  for (const file of files) {
                    if (file.type === 'file' && (file.name.endsWith('.md') || file.name.endsWith('.txt'))) {
                      try {
                        const content = await githubService.getFileContent(
                          repo.owner.login,
                          repo.name,
                          file.path
                        );
                        gitService.createFile(file.path, content);
                        pulledFiles.set(file.path, content);
                      } catch (error) {
                        console.warn(`跳过文件 ${file.path}:`, error);
                      }
                    }
                  }

                  gitService.updateBaseline(pulledFiles);
                  console.log(`✅ 自动拉取了 ${pulledFiles.size} 个文件`);
                }
              } catch (error) {
                console.error('❌ 自动拉取失败:', error);
              } finally {
                setLoading(false);
              }
            }

            // 重新加载仓库信息和提交历史
            loadRepoInfo();
          }}
        />
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-3 border-b border-border">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium">提交历史</h3>
          <div className="flex items-center space-x-0.5">
            {/* 切换仓库按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                console.log('🔄 切换仓库按钮被点击');
                console.log('🔄 当前showRepoSelector状态:', showRepoSelector);
                setShowRepoSelector(true);
                console.log('🔄 已设置showRepoSelector为true');
              }}
              title={currentRepo ? "切换仓库" : "选择仓库"}
              className="h-7 w-7 p-0"
            >
              <ArrowLeftRight className="w-3.5 h-3.5" />
            </Button>

            {/* 仓库信息按钮 */}
            {currentRepo && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  alert(`仓库: ${currentRepo.owner}/${currentRepo.repo}\n分支: ${selectedBranch || currentRepo.branch || 'main'}`);
                }}
                title="仓库信息"
                className="h-7 w-7 p-0"
              >
                <Info className="w-3.5 h-3.5" />
              </Button>
            )}

            {/* 刷新按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => loadCommits()}
              disabled={loading}
              title="刷新"
              className="h-7 w-7 p-0"
            >
              <RefreshCw className={`w-3.5 h-3.5 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>

        {currentRepo && (
          <div className="space-y-2">
            {/* 分支控制器 - 仿照Git面板样式 */}
            <div className="relative branch-dropdown-container">
              <Button
                variant="ghost"
                size="sm"
                className="w-full justify-between text-xs font-normal h-7 px-2"
                title="切换分支"
                onClick={() => {
                  console.log('🌿 分支按钮被点击，当前状态:', showBranchDropdown);
                  console.log('🌿 当前分支列表:', branches);
                  setShowBranchDropdown(!showBranchDropdown);
                }}
              >
                <div className="flex items-center space-x-1.5">
                  <GitBranch className="w-3 h-3" />
                  <span className="truncate">{selectedBranch || currentRepo.branch || 'main'}</span>
                </div>
                <ChevronDown className="w-3 h-3" />
              </Button>

              {/* 分支下拉菜单 */}
              {showBranchDropdown && (
                <div className="absolute top-full left-0 right-0 mt-1 bg-popover border border-border rounded-md shadow-lg z-50 max-h-40 overflow-y-auto">
                  {branches.length > 0 ? (
                    branches.map((branch) => (
                      <button
                        key={branch}
                        className={`w-full text-left px-3 py-1.5 text-xs hover:bg-accent hover:text-accent-foreground ${
                          branch === selectedBranch ? 'bg-accent text-accent-foreground' : 'text-popover-foreground'
                        }`}
                        onClick={() => handleBranchChange(branch)}
                      >
                        <div className="flex items-center space-x-2">
                          <GitBranch className="w-3 h-3" />
                          <span className="truncate">{branch}</span>
                          {branch === selectedBranch && (
                            <span className="ml-auto text-xs text-muted-foreground">✓</span>
                          )}
                        </div>
                      </button>
                    ))
                  ) : (
                    <div className="px-3 py-2 text-xs text-muted-foreground">
                      加载分支中...
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* 仓库信息 */}
            <div className="text-xs text-muted-foreground">
              {currentRepo.owner}/{currentRepo.repo}
            </div>

            {/* 搜索框 */}
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-muted-foreground" />
              <Input
                placeholder="搜索提交消息或作者..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="text-xs pl-7 h-7"
              />
            </div>
          </div>
        )}
      </div>

      {/* 提交列表 */}
      <ScrollArea className="flex-1">
        <div className="p-2">
          {filteredCommits.length > 0 ? (
            <div className="space-y-1">
              {filteredCommits.map((commit, index) => (
                <div
                  key={commit.sha}
                  className={`group relative p-2 text-xs border rounded cursor-pointer transition-colors hover:bg-muted/50 ${
                    selectedCommit?.sha === commit.sha ? 'bg-muted border-primary' : ''
                  }`}
                  onClick={() => handleCommitSelect(commit)}
                >
                  {/* 提交消息 */}
                  <div className="font-medium truncate mb-1" title={commit.commit.message}>
                    {commit.commit.message.split('\n')[0]}
                  </div>

                  {/* 作者和时间信息 */}
                  <div className="flex items-center justify-between text-muted-foreground mb-1">
                    <div className="flex items-center space-x-1 min-w-0 flex-1">
                      {commit.author?.avatar_url ? (
                        <img
                          src={commit.author.avatar_url}
                          alt={commit.author.login}
                          className="w-3 h-3 rounded-full flex-shrink-0"
                        />
                      ) : (
                        <User className="w-3 h-3 flex-shrink-0" />
                      )}
                      <span className="truncate" title={commit.author?.login || commit.commit.author.name}>
                        {commit.author?.login || commit.commit.author.name}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1 flex-shrink-0">
                      <Clock className="w-3 h-3" />
                      <span>{formatDate(commit.commit.author.date)}</span>
                    </div>
                  </div>

                  {/* SHA */}
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-muted-foreground font-mono">
                      {commit.sha.substring(0, 7)}
                    </div>
                  </div>
                </div>
              ))}

              {hasMore && (
                <div className="p-3 text-center border-t border-border">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadMoreCommits}
                    disabled={loading}
                    className="w-full h-8 text-xs"
                  >
                    {loading ? (
                      <div className="flex items-center space-x-1">
                        <RefreshCw className="w-3 h-3 animate-spin" />
                        <span>加载中...</span>
                      </div>
                    ) : (
                      <div className="flex items-center space-x-1">
                        <span>加载更多提交</span>
                      </div>
                    )}
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="p-8 text-center">
              {loading ? (
                <div className="flex flex-col items-center space-y-3">
                  <RefreshCw className="w-6 h-6 animate-spin text-muted-foreground" />
                  <div className="text-sm text-muted-foreground">加载提交历史中...</div>
                </div>
              ) : (
                <div className="flex flex-col items-center space-y-3">
                  <GitCommit className="w-8 h-8 text-muted-foreground" />
                  <div className="space-y-1">
                    <div className="text-sm font-medium">没有找到提交记录</div>
                    <div className="text-xs text-muted-foreground">
                      {searchQuery ? '尝试调整搜索条件' : '该仓库可能没有提交历史'}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </ScrollArea>

      {/* GitHub仓库选择器 - 用于切换仓库 */}
      <RepoSelector
        open={showRepoSelector}
        onOpenChange={setShowRepoSelector}
        onRepoSelected={handleRepoSelected}
      />
    </div>
  );
}
