'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { SimpleGitService, GitConfig } from '@/lib/simpleGit';

interface GitConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfigSaved?: (config: GitConfig) => void;
}

export function GitConfigDialog({ open, onOpenChange, onConfigSaved }: GitConfigDialogProps) {
  const [config, setConfig] = useState<GitConfig>({
    name: '',
    email: '',
    token: ''
  });
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (open) {
      // 加载现有配置
      const gitService = SimpleGitService.getInstance();
      const existingConfig = gitService.getConfig();
      if (existingConfig) {
        setConfig(existingConfig);
      }
    }
  }, [open]);

  const handleSave = async () => {
    if (!config.name.trim() || !config.email.trim()) {
      alert('请填写用户名和邮箱');
      return;
    }

    setLoading(true);
    try {
      const gitService = SimpleGitService.getInstance();
      gitService.setConfig(config);

      if (onConfigSaved) {
        onConfigSaved(config);
      }

      onOpenChange(false);
      alert('配置保存成功！');
    } catch (error) {
      console.error('Save config failed:', error);
      alert('保存配置失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Git 配置</DialogTitle>
          <DialogDescription>
            设置您的Git用户信息和GitHub访问令牌
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="name" className="text-right">
              用户名
            </Label>
            <Input
              id="name"
              value={config.name}
              onChange={(e) => setConfig({ ...config, name: e.target.value })}
              className="col-span-3"
              placeholder="您的Git用户名"
            />
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="email" className="text-right">
              邮箱
            </Label>
            <Input
              id="email"
              type="email"
              value={config.email}
              onChange={(e) => setConfig({ ...config, email: e.target.value })}
              className="col-span-3"
              placeholder="您的Git邮箱"
            />
          </div>
          
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="token" className="text-right">
              GitHub Token
            </Label>
            <Input
              id="token"
              type="password"
              value={config.token || ''}
              onChange={(e) => setConfig({ ...config, token: e.target.value })}
              className="col-span-3"
              placeholder="GitHub Personal Access Token (可选)"
            />
          </div>
          
          <div className="text-sm text-muted-foreground">
            <p>• GitHub Token用于访问私有仓库和推送代码</p>
            <p>• 可在GitHub Settings → Developer settings → Personal access tokens中创建</p>
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            {loading ? '保存中...' : '保存'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
