"use client";
import React, { createContext, useContext, useRef, useCallback } from "react";

type Listener = (doc: any) => void;

type DocCtx = {
  getPmdoc: () => any | null;
  subscribe: (fn: Listener) => () => void;   // 订阅 PM-Doc 变化
  _registerGetter: (fn: () => any | null) => void; // 由编辑器注册 getter
  _emitChange: (doc: any) => void;           // 由编辑器发送变更事件
};

const Ctx = createContext<DocCtx | null>(null);

export function DocProvider({ children }: { children: React.ReactNode }) {
  const getterRef = useRef<() => any | null>(() => null);
  const listeners = useRef<Set<Listener>>(new Set());

  const subscribe = useCallback((fn: Listener) => {
    listeners.current.add(fn);
    return () => listeners.current.delete(fn);
  }, []);

  const _emitChange = useCallback((doc: any) => {
    for (const l of listeners.current) l(doc);
  }, []);

  const ctx: DocCtx = {
    getPmdoc: () => getterRef.current?.() ?? null,
    subscribe,
    _registerGetter: (fn) => { getterRef.current = fn; },
    _emitChange,
  };

  return <Ctx.Provider value={ctx}>{children}</Ctx.Provider>;
}

export function useDocCtx() {
  const v = useContext(Ctx);
  if (!v) throw new Error("DocProvider missing");
  return v;
}
