下面给你一份**从零到一可落地**的「PR-1 → PR-3 完整方案」。
遵循你们的约束：**Next.js 15.4.5 + React 19.1.1 + Milkdown 7.15.x**，不做选择题，只给**唯一最优路径**，并附 **代码骨架、命令、验收标准与排障**。直接按顺序做即可。

---

## 0) 基线与依赖（一次到位）

**Node / npm**

* Node ≥ 20.11，npm ≥ 10。

**安装命令**

```bash
# 核心依赖（Milkdown 7.x + GFM）
npm i -E @milkdown/core@^7 @milkdown/react@^7 @milkdown/prose@^7 \
         @milkdown/preset-commonmark@^7 @milkdown/preset-gfm@^7 \
         @milkdown/plugin-listener@^7

# React 19 兼容配套
npm i -E next-themes@^0.4.6
# 如用 Radix UI，请统一升级到 ≥ 1.2.x（示例）
npm i -E @radix-ui/react-dialog@^1.2.11 @radix-ui/react-popover@^1.2.11

# 建议：统一版本，避免重复副本
npm pkg set overrides.react=19.1.1 overrides.react-dom=19.1.1
npm pkg set overrides."@types/react"=19.1.9 overrides."@types/react-dom"=19.1.7

# 清缓存并安装
rm -rf node_modules package-lock.json && npm cache verify && npm i
```

**请勿安装：** `@milkdown/plugin-table`（老包，已废弃）
**关键导入：** `editorViewCtx` 从 **@milkdown/core** 导入，不是 @milkdown/prose。

---

# PR-1 —— 接入 Milkdown，打通四模式外壳

> 目标：**实时可写**；Preview/Split 暂以 **PM-Doc JSON** 占位；四模式可切换；不触发旧的全文渲染管道。

### 目录骨架

```
src/
  app/editor/page.tsx
  components/
    EditorShell.tsx
    RealtimeMode.tsx
    PreviewMode.tsx
    SplitMode.tsx
    SourceMode.tsx
    editor/doc/DocContext.tsx
  editor/milkdown.tsx
```

### 1) DocContext（提供文档获取；PR-2 再加订阅）

```tsx
// src/components/editor/doc/DocContext.tsx
"use client";
import React, { createContext, useContext, useRef } from "react";

type DocCtx = {
  getPmdoc: () => any | null;
  _registerGetter: (fn: () => any | null) => void;
};
const Ctx = createContext<DocCtx | null>(null);

export function DocProvider({ children }: { children: React.ReactNode }) {
  const getterRef = useRef<() => any | null>(() => null);
  return (
    <Ctx.Provider value={{
      getPmdoc: () => getterRef.current?.() ?? null,
      _registerGetter: (fn) => { getterRef.current = fn; },
    }}>{children}</Ctx.Provider>
  );
}
export const useDocCtx = () => {
  const v = useContext(Ctx); if (!v) throw new Error("DocProvider missing");
  return v;
};
```

### 2) Milkdown（7.x 正确用法：Provider + useEditor 工厂）

```tsx
// src/editor/milkdown.tsx
"use client";
import { useEffect, useMemo } from "react";
import { MilkdownProvider, Milkdown, useEditor } from "@milkdown/react";
import { Editor, rootCtx, editorViewCtx, defaultValueCtx } from "@milkdown/core";
import { commonmark } from "@milkdown/preset-commonmark";
import { gfm } from "@milkdown/preset-gfm";
import { useDocCtx } from "@/components/editor/doc/DocContext";

export function MilkdownEditor({ initial = "# 标题\n\n这里开始输入…" }:{ initial?: string }) {
  return (
    <MilkdownProvider>
      <EditorInner initial={initial}/>
    </MilkdownProvider>
  );
}

function EditorInner({ initial }: { initial: string }) {
  const { _registerGetter } = useDocCtx();

  const factory = useMemo(
    () => (root: HTMLElement) =>
      Editor.make()
        .use(commonmark)
        .use(gfm)
        .config((ctx) => {
          ctx.set(rootCtx, root);
          ctx.set(defaultValueCtx, initial);
        }),
    [initial]
  );
  const { get } = useEditor(factory, [factory]);

  useEffect(() => {
    const editor = get(); if (!editor) return;
    editor.action((ctx) => {
      const view = ctx.get(editorViewCtx);
      _registerGetter(() => view.state.doc.toJSON());
    });
  }, [get, _registerGetter]);

  return <Milkdown/>;
}
```

### 3) 四模式外壳（PR-1 先用 JSON 占位）

```tsx
// src/components/RealtimeMode.tsx
"use client";
import dynamic from "next/dynamic";
const MilkdownEditor = dynamic(() => import("@/editor/milkdown").then(m => m.MilkdownEditor), { ssr:false });
export default function RealtimeMode(){
  return <div className="h-full min-h-0 overflow-auto"><MilkdownEditor/></div>;
}

// src/components/PreviewMode.tsx
"use client";
import { useEffect, useState } from "react";
import { useDocCtx } from "./editor/doc/DocContext";
export default function PreviewMode(){
  const { getPmdoc } = useDocCtx();
  const [json, setJson] = useState<any|null>(null);
  useEffect(()=>{
    const tick = () => setJson(getPmdoc());
    tick(); const t = setInterval(tick, 500); // PR-1 简单轮询
    return ()=>clearInterval(t);
  }, [getPmdoc]);
  if (!json) return <div className="p-4 text-gray-500">等待编辑器初始化…</div>;
  return <pre className="h-full overflow-auto p-4">{JSON.stringify(json, null, 2)}</pre>;
}

// src/components/SplitMode.tsx
"use client";
import RealtimeMode from "./RealtimeMode"; import PreviewMode from "./PreviewMode";
export default function SplitMode(){
  return (
    <div className="h-full grid grid-cols-2">
      <div className="min-h-0"><RealtimeMode/></div>
      <div className="min-h-0 border-l"><PreviewMode/></div>
    </div>
  );
}

// src/components/SourceMode.tsx
"use client";
export default function SourceMode(){ return <div className="p-4 text-gray-600">源码模式（PR-3 再接 CM6）</div>; }

// src/components/EditorShell.tsx
"use client";
import { useState } from "react"; import { DocProvider } from "./editor/doc/DocContext";
import RealtimeMode from "./RealtimeMode"; import PreviewMode from "./PreviewMode";
import SplitMode from "./SplitMode"; import SourceMode from "./SourceMode";
type Mode="realtime"|"preview"|"split"|"source";
export default function EditorShell(){
  const [mode,setMode] = useState<Mode>("realtime");
  return (
    <DocProvider>
      <div className="h-screen flex flex-col">
        <header className="flex items-center gap-3 p-3 border-b">
          {(["realtime","preview","split","source"] as Mode[]).map(m =>
            <button key={m} onClick={()=>setMode(m)}>{m}</button>)}
          <span className="ml-auto text-gray-500">PR-1</span>
        </header>
        <main className="flex-1 overflow-hidden">
          {/* 常驻实时编辑器，切换仅隐藏（后续状态不丢） */}
          <div className={(mode==="realtime"||mode==="split")?"block":"hidden"}><RealtimeMode/></div>
          {mode==="preview" && <PreviewMode/>}
          {mode==="split"   && <div/>/* preview 已在上面右栏 */}
          {mode==="source"  && <SourceMode/>}
        </main>
      </div>
    </DocProvider>
  );
}

// src/app/editor/page.tsx
"use client";
import EditorShell from "@/components/EditorShell";
export default function Page(){ return <EditorShell/>; }
```

### 4) PR-1 验收

* 进入 `/editor` 可正常输入（中文 IME 正常）。
* 切换四模式无报错；Preview/Split 看到 **PM-Doc JSON**（不再是 null）。

---

# PR-2 —— 统一渲染内核 + Web Worker + 复制到公众号

> 目标：右侧**微信友好 HTML**（内联样式），**事件驱动**更新；提供 **一键复制**。
> 关键：Worker 文件与调用者**同目录**，用 `new URL("./xxx.worker.ts", import.meta.url)`（Webpack 5 静态分析）。

### 目录新增

```
src/editor/render/{tokens.ts,inline-style.ts,blocks.ts,whitelist.ts}
src/editor/workers/{protocol.ts}
src/components/{wechatRender.worker.ts, WechatCopyButton.tsx}
```

### 1) 渲染内核（最小可用，内联样式）

```ts
// src/editor/render/tokens.ts
export const TOKENS = { brand:'#16a34a', text:'#1f2937', heading:'#0f172a', link:'#166534',
  codeBg:'#f8fafc', codeBorder:'#e5e7eb', quoteBar:'#86efac',
  tableBorder:'#d1d5db', tableHeaderBg:'#f0fdf4',
  font:{ base:16, h1:28, h2:22, h3:18 }, line:{ base:1.8, heading:1.4, code:1.6 },
  space:{ p:12, li:8, td:'8px 12px' }, radius:{ img:'8px', code:'6px' } } as const;

// src/editor/render/inline-style.ts
export const css = (o:Record<string,string|number|undefined>) =>
  Object.entries(o).filter(([,v])=>v!==undefined).map(([k,v])=>`${k}:${v}`).join(';');
export const esc = (s:string) =>
  s.replace(/[&<>"']/g, c=>({ "&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}[c] as string));

// src/editor/render/blocks.ts
import { TOKENS as T } from "./tokens"; import { css, esc } from "./inline-style";
export const renderParagraph = (inner:string) =>
  `<p style="${css({"font-size":`${T.font.base}px`,"line-height":T.line.base,color:T.text,margin:`0 0 ${T.space.p}px 0`})}">${inner}</p>`;
export const renderHeading = (lvl:1|2|3, inner:string) => {
  const fs = lvl===1?T.font.h1:lvl===2?T.font.h2:T.font.h3;
  return `<h${lvl} style="${css({"font-size":`${fs}px`,"line-height":T.line.heading,color:T.heading,margin:"24px 0 12px"})}">${inner}</h${lvl}>`;
};
export const renderBlockquote = (inner:string) =>
  `<blockquote style="${css({"border-left":`4px solid ${T.quoteBar}`,"margin":"12px 0","padding":"6px 12px"})}">${inner}</blockquote>`;
export const renderCodeBlock = (codeHtml:string) =>
  `<pre style="${css({background:T.codeBg,border:`1px solid ${T.codeBorder}`,"border-radius":T.radius.code,padding:"12px",overflow:"auto"})}"><code>${codeHtml}</code></pre>`;
export const renderImage = (src:string, alt?:string) =>
  `<img src="${esc(src)}" alt="${esc(alt??"")}" style="${css({"max-width":"100%",height:"auto","border-radius":T.radius.img})}"/>`;
export const renderList = (ordered:boolean, items:string[]) =>
  ordered ? `<ol>${items.map(i=>`<li>${i}</li>`).join("")}</ol>` : `<ul>${items.map(i=>`<li>${i}</li>`).join("")}</ul>`;
export const renderTable = (thead:string, tbody:string) =>
  `<table style="${css({width:"100%","border-collapse":"collapse","table-layout":"fixed",border:`1px solid ${T.tableBorder}`})}">${thead}${tbody}</table>`;
```

### 2) Worker 协议 & 实现（整篇渲染）

```ts
// src/editor/workers/protocol.ts
export type RenderReq = { type:"render:full"; pmdoc:any };
export type RenderRes = { type:"render:full:ok"; html:string } | { type:"error"; message:string };
```

```ts
// src/components/wechatRender.worker.ts（与 PreviewMode.tsx 同目录）
/* eslint-disable no-restricted-globals */
/// <reference lib="webworker" />
export {}; // ESM
import { renderParagraph, renderHeading, renderBlockquote, renderCodeBlock, renderImage, renderList, renderTable } from "@/editor/render/blocks";
import { esc } from "@/editor/render/inline-style";
import type { RenderReq, RenderRes } from "@/editor/workers/protocol";

function inline(node:any):string{
  return (node.content||[]).map((n:any)=>{
    if(n.type==="text"){
      let t = esc(n.text||"");
      (n.marks||[]).forEach((m:any)=>{
        if(m.type==="strong") t=`<strong>${t}</strong>`;
        if(m.type==="em")     t=`<em>${t}</em>`;
        if(m.type==="code")   t=`<code>${t}</code>`;
        if(m.type==="link")   t=`<a href="${esc(m.attrs?.href||"#")}" title="${esc(m.attrs?.title||"")}">${t}</a>`;
      });
      return t;
    }
    return nodeToHtml(n);
  }).join("");
}
function thead(node:any){ const th = (node.content||[]).find((n:any)=>n.type==="table_header");
  if(!th) return ""; const rows=(th.content||[]).map((tr:any)=>`<tr>${(tr.content||[]).map((th:any)=>`<th>${inline(th)}</th>`).join("")}</tr>`).join("");
  return `<thead>${rows}</thead>`; }
function tbody(node:any){ const tb = (node.content||[]).find((n:any)=>n.type==="table_body");
  if(!tb) return ""; const rows=(tb.content||[]).map((tr:any)=>`<tr>${(tr.content||[]).map((td:any)=>`<td>${inline(td)}</td>`).join("")}</tr>`).join("");
  return `<tbody>${rows}</tbody>`; }
function nodeToHtml(node:any):string{
  switch(node.type){
    case "paragraph":    return renderParagraph(inline(node));
    case "heading":      return renderHeading(Math.min(Math.max(node.attrs?.level||1,1),3) as 1|2|3, inline(node));
    case "blockquote":   return renderBlockquote((node.content||[]).map(nodeToHtml).join(""));
    case "code_block":   return renderCodeBlock(esc(node.text || node.content?.[0]?.text || ""));
    case "image":        return renderImage(node.attrs?.src, node.attrs?.alt);
    case "bullet_list":  return renderList(false, (node.content||[]).map(nodeToHtml));
    case "ordered_list": return renderList(true,  (node.content||[]).map(nodeToHtml));
    case "list_item":    return (node.content||[]).map(nodeToHtml).join("");
    case "table":        return renderTable(thead(node), tbody(node));
    default:             return (node.content||[]).map(nodeToHtml).join("");
  }
}

self.onmessage = (e: MessageEvent<RenderReq>) => {
  try{
    if (e.data.type==="render:full") {
      const html = `<article>${(e.data.pmdoc.content||[]).map(nodeToHtml).join("")}</article>`;
      (self as any).postMessage({ type:"render:full:ok", html } as RenderRes);
    }
  }catch(err:any){
    (self as any).postMessage({ type:"error", message:String(err?.message||err) } as RenderRes);
  }
};
```

### 3) 预览替换为 Worker 产物 + 一键复制

```tsx
// src/components/PreviewMode.tsx
"use client";
import { useEffect, useRef } from "react";
import { useDocCtx } from "./editor/doc/DocContext";
import type { RenderReq, RenderRes } from "@/editor/workers/protocol";

export default function PreviewMode(){
  const { getPmdoc } = useDocCtx();
  const el = useRef<HTMLDivElement>(null);

  useEffect(()=>{
    const worker = new Worker(new URL("./wechatRender.worker.ts", import.meta.url)); // 同目录
    const onMsg = (e: MessageEvent<RenderRes>) => {
      if (e.data?.type==="render:full:ok" && el.current) el.current.innerHTML = e.data.html;
    };
    worker.addEventListener("message", onMsg);
    const p = getPmdoc(); if (p) worker.postMessage({ type:"render:full", pmdoc: p } satisfies RenderReq);
    return ()=>{ worker.removeEventListener("message", onMsg); worker.terminate(); };
  }, [getPmdoc]);

  return <div id="wechat-preview" ref={el} className="h-full overflow-auto p-4"/>;
}
```

```tsx
// src/components/WechatCopyButton.tsx
"use client";
export default function WechatCopyButton(){
  const onCopy = async () => {
    const inner = document.getElementById("wechat-preview")?.innerHTML ?? "";
    const html = `<article>${inner}</article>`;
    try{
      await navigator.clipboard.write([ new (window as any).ClipboardItem({ "text/html": new Blob([html], { type:"text/html" }) }) ]);
      alert("已复制为 HTML，直接粘贴到公众号。");
    }catch{
      await navigator.clipboard.writeText(inner.replace(/<[^>]+>/g,""));
      alert("浏览器限制：已复制纯文本。");
    }
  };
  return <button onClick={onCopy} className="ml-3">复制到公众号</button>;
}
```

> 若你的头部工具区未加按钮，在 `EditorShell` 的 header 里引入 `<WechatCopyButton/>`。

### 4) PR-2 验收

* Preview/Split 显示 **微信友好 HTML**（段落/标题/列表/引用/代码块/表格/图片）。
* 左侧输入后 **≤300ms** 右侧更新（PR-2 暂以整篇重绘实现，PR-3 做增量）。
* “复制到公众号” 能在 mp.weixin.qq.com 粘贴基本不掉样。
* **Worker 路径不 404**：DevTools Network 中看到 `/_next/static/chunks/...worker.js`。

---

# PR-3 —— 性能与稳定性（增量渲染 + 不丢字 + 不丢状态）

> 目标：**按块增量渲染**、中文 IME 合成态不丢字、滚动流畅；**不做 schema 回灌**，避免你们曾遇到的 PM JSON 兼容问题。

### 目录新增/修改

```
新增：src/lib/perf/rafThrottle.ts
新增：src/components/preview-domdiff.ts
新增：src/components/types.ts
修改：src/editor/workers/protocol.ts   # 加 “render:blocks”
修改：src/components/wechatRender.worker.ts  # 追加按块渲染分支
修改：src/components/PreviewMode.tsx  # 改为“首次整篇 + 后续按块”
修改：src/editor/milkdown.tsx         # 用 listener 插件发事件（mounted/updated + compositionend）
```

### 1) 协议：加“按块渲染”

```ts
// src/editor/workers/protocol.ts
export type BlockHtml = { id: string; html: string };
export type RenderReq =
  | { type:"render:full"; pmdoc:any }
  | { type:"render:blocks"; pmdoc:any; ids:string[] };
export type RenderRes =
  | { type:"render:full:ok"; html:string }
  | { type:"render:blocks:ok"; blocks: BlockHtml[] }
  | { type:"error"; message:string };
```

### 2) Worker：复用 nodeToHtml，返回指定顶层块

```ts
// src/components/wechatRender.worker.ts 追加：
import type { RenderReq, RenderRes } from "@/editor/workers/protocol";
self.addEventListener("message", (e: MessageEvent<RenderReq>) => {
  try{
    if (e.data.type==="render:blocks") {
      const top = (e.data.pmdoc?.content ?? []) as any[];
      const blocks = e.data.ids.map(id => {
        const idx = Number(id); const node = top[idx];
        const html = node ? nodeToHtml(node) : "";
        return { id, html };
      });
      (self as any).postMessage({ type:"render:blocks:ok", blocks } as RenderRes);
      return;
    }
    // 既有的 render:full 保持不变
  }catch(err:any){
    (self as any).postMessage({ type:"error", message:String(err?.message||err) } as RenderRes);
  }
});
```

### 3) 预览端：首次整篇 + 之后按块 + DOM 合并

```ts
// src/components/types.ts
export type BlockHtml = { id: string; html: string };
```

```ts
// src/components/preview-domdiff.ts
import type { BlockHtml } from "./types";
export function mergeBlocks(container: HTMLElement, blocks: BlockHtml[]) {
  const map = new Map<string, HTMLElement>();
  container.querySelectorAll<HTMLElement>('[data-bid]').forEach(el => { if(el.dataset.bid) map.set(el.dataset.bid, el); });
  for (const b of blocks) {
    const exist = map.get(b.id);
    if (!exist) { const el = document.createElement("div"); el.dataset.bid=b.id; el.innerHTML=b.html; container.appendChild(el); }
    else exist.innerHTML = b.html;
  }
}
```

```tsx
// src/components/PreviewMode.tsx（替换核心逻辑）
"use client";
import { useEffect, useRef } from "react";
import { useDocCtx } from "./editor/doc/DocContext";
import type { RenderReq, RenderRes } from "@/editor/workers/protocol";
import { mergeBlocks } from "./preview-domdiff";

export default function PreviewMode(){
  const { getPmdoc, subscribe } = useDocCtx();
  const el = useRef<HTMLDivElement>(null);

  useEffect(()=>{
    const worker = new Worker(new URL("./wechatRender.worker.ts", import.meta.url));
    const onMsg = (e: MessageEvent<RenderRes>) => {
      if (e.data?.type==="render:full:ok" && el.current) el.current.innerHTML = e.data.html;
      if (e.data?.type==="render:blocks:ok" && el.current) mergeBlocks(el.current, e.data.blocks);
    };
    worker.addEventListener("message", onMsg);

    const p = getPmdoc(); if (p) worker.postMessage({ type:"render:full", pmdoc: p } satisfies RenderReq);

    let prevSig: string[] = signatureOf(p);
    const unsub = subscribe((doc)=>{
      const nextSig = signatureOf(doc);
      const ids = diffIds(prevSig, nextSig); prevSig = nextSig;
      if (ids.length) worker.postMessage({ type:"render:blocks", pmdoc: doc, ids } satisfies RenderReq);
    });

    return ()=>{ unsub(); worker.removeEventListener("message", onMsg); worker.terminate(); };
  }, [getPmdoc, subscribe]);

  return <div id="wechat-preview" ref={el} className="h-full overflow-auto p-4" />;
}

// —— 简单块签名与差异计算 —— 
function signatureOf(pmDoc:any): string[]{
  const top=(pmDoc?.content??[]) as any[];
  return top.map(n=>`${n?.type||""}|${firstText(n).slice(0,40)}`);
}
function firstText(node:any):string{
  if(!node) return ""; if(node.type==="text") return node.text||"";
  const arr=(node.content||[]) as any[]; for(const c of arr){ const s=firstText(c); if(s) return s; }
  return "";
}
function diffIds(prev:string[], next:string[]):string[]{
  const out:string[]=[]; const max=Math.max(prev.length,next.length);
  for(let i=0;i<max;i++) if(prev[i]!==next[i]) out.push(String(i)); return out;
}
```

### 4) 编辑器端：用官方 listener 发事件 + IME 兜底

（**不覆盖 dispatchTransaction，不做 schema 回灌**）

```tsx
// src/editor/milkdown.tsx（在 PR-1 基础上改动）
"use client";
import { useEffect, useMemo } from "react";
import { MilkdownProvider, Milkdown, useEditor } from "@milkdown/react";
import { Editor, rootCtx, editorViewCtx, defaultValueCtx } from "@milkdown/core";
import { commonmark } from "@milkdown/preset-commonmark";
import { gfm } from "@milkdown/preset-gfm";
import { listener, listenerCtx } from "@milkdown/plugin-listener";
import { useDocCtx } from "@/components/editor/doc/DocContext";

export function MilkdownEditor({ initial = "# 标题\n\n这里开始输入…" }:{ initial?: string }) {
  return (
    <MilkdownProvider>
      <EditorInner initial={initial}/>
    </MilkdownProvider>
  );
}

function EditorInner({ initial }: { initial: string }) {
  const { _registerGetter, _emitChange } = useDocCtx();

  const factory = useMemo(
    () => (root: HTMLElement) =>
      Editor.make()
        .use(commonmark).use(gfm).use(listener)
        .config((ctx) => {
          ctx.set(rootCtx, root);
          ctx.set(defaultValueCtx, initial);

          const lm = ctx.get(listenerCtx);
          lm.mounted((_, view) => { _emitChange(view.state.doc.toJSON()); });
          lm.updated((_, doc) => { _emitChange(doc.toJSON()); });
        }),
    [initial, _emitChange]
  );
  const { get } = useEditor(factory, [factory]);

  useEffect(() => {
    const editor = get(); if (!editor) return;
    editor.action((ctx) => {
      const view = ctx.get(editorViewCtx);
      _registerGetter(() => view.state.doc.toJSON());
      view.dom.addEventListener("compositionend", () => { _emitChange(view.state.doc.toJSON()); });
    });
  }, [get, _registerGetter, _emitChange]);

  return <Milkdown/>;
}
```

### 5) 样式与布局要点（避免“无滚动条”）

* `SplitMode` 两栏：`className="min-h-0"`
* `RealtimeMode` 外层：`className="h-full min-h-0 overflow-auto"`
* **不要**把滚动加到 `.ProseMirror`；滚动层应是其父级。

### 6) PR-3 验收

* 10k 行滚动 **≥55FPS**，输入不卡顿，中文 IME 不丢字；
* 右侧只对**变更块**更新（Network 仅见 `render:blocks` 消息）；
* 切换模式不丢状态（实时编辑器**常驻**，CSS 隐藏即可）。

---

## 常见坑 & 快速排障

1. **`editorViewCtx` 导入错误** → 从 `@milkdown/core` 导入。
2. **`@milkdown/plugin-table` 不存在** → 用 `@milkdown/preset-gfm`。
3. **Worker 404** → Worker 文件与组件**同目录**；`new URL("./xxx.worker.ts", import.meta.url)` 字面量；不要跨目录/变量化路径。
4. **React 19 兼容性** → `next-themes@^0.4.6`，Radix 全部 ≥1.2.x；用 `overrides` 锁定 `react/react-dom` 19.1.1。
5. **左右不同步** → 打三段日志：

   * `milkdown.tsx`：`console.log('[emit]')`（mounted/updated/compositionend）
   * `PreviewMode`：`console.log('[recv]')`（subscribe 回调）
   * Worker 回包：`console.log('[merge]')`（blocks ids）
     有发→有收→有合并，即闭环通了。
6. **无滚动条** → 检查 `min-h-0` 链路是否断、滚动层是否在 `.ProseMirror` 的父级。

---

## 交付节奏（建议）

* **PR-1**：接入 Milkdown + 四模式 + JSON 占位（1 人日）。
* **PR-2**：Worker 渲染 + 复制到公众号（1–2 人日）。
* **PR-3**：按块增量 + listener 同步 + IME 兜底 + 滚动/性能（2 人日）。

每步提交都附「验收截图 + 三段调试日志」，便于快速回归。

---

需要我把上面的关键文件（`milkdown.tsx / PreviewMode.tsx / wechatRender.worker.ts / protocol.ts`）打成 **git patch** 版，或对你们现有仓库路径做一次**定制化差异补丁**，我可以马上给。你也可以把当前分支推上来（或贴路径差异），我按你们真实结构输出可直接 `git apply` 的补丁。
