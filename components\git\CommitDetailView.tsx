'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import {
  GitCommit,
  User,
  Calendar,
  FileText,
  Eye,
  Download,
  Plus,
  Minus,
  Clock,
  GitBranch,
} from 'lucide-react';
import { GitHubService } from '@/lib/github';
import { SimpleGitService } from '@/lib/simpleGit';
import { useSession } from 'next-auth/react';

interface CommitDetailViewProps {
  commitData: any;
  filename: string;
  onFileSelect?: (filepath: string) => void;
}

export function CommitDetailView({ commitData, filename, onFileSelect }: CommitDetailViewProps) {
  const { data: session } = useSession();
  const [gitService] = useState(() => SimpleGitService.getInstance());
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [fileContent, setFileContent] = useState<string>('');
  const [loading, setLoading] = useState(false);

  let commit: any, files: any[], stats: any;
  
  try {
    const data = JSON.parse(commitData);
    commit = data.commit;
    files = data.files || [];
    stats = data.stats;
  } catch (error) {
    // 如果不是JSON格式，可能是文件内容
    return (
      <div className="h-full flex flex-col">
        <div className="p-4 border-b border-border">
          <div className="flex items-center space-x-2">
            <FileText className="w-4 h-4" />
            <span className="text-sm font-medium">{filename}</span>
          </div>
        </div>
        <ScrollArea className="flex-1">
          <div className="p-4">
            <pre className="text-sm whitespace-pre-wrap break-words">
              {commitData}
            </pre>
          </div>
        </ScrollArea>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return '今天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN', { 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const handleFileSelect = async (filepath: string) => {
    if (!session?.accessToken || !commit) return;

    setSelectedFile(filepath);
    setFileContent('');
    setLoading(true);

    try {
      const currentRepo = gitService.getCurrentRepo();
      if (!currentRepo) return;

      const githubService = new GitHubService(session.accessToken);
      const content = await githubService.getFileContentAtCommit(
        currentRepo.owner,
        currentRepo.repo,
        filepath,
        commit.sha
      );
      setFileContent(content);
    } catch (error) {
      console.error('获取文件内容失败:', error);
      setFileContent('无法获取文件内容');
    } finally {
      setLoading(false);
    }
  };

  const handleRestoreFile = async (filepath: string, content: string) => {
    if (!filepath || !content) return;

    const confirmRestore = confirm(
      `确定要将文件 "${filepath}" 恢复到此提交的版本吗？\n\n` +
      `这将覆盖当前文件内容。建议先保存当前更改。`
    );

    if (confirmRestore) {
      try {
        // 更新本地文件内容
        gitService.updateFile(filepath, content);
        
        // 如果有文件选择回调，重新选择文件以更新编辑器
        if (onFileSelect) {
          onFileSelect(filepath);
        }
        
        alert(`文件已恢复到提交 ${commit.sha.substring(0, 7)} 的版本`);
      } catch (error) {
        console.error('恢复文件失败:', error);
        alert('恢复文件失败，请重试');
      }
    }
  };

  if (!commit) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center space-y-4 max-w-md">
          <GitCommit className="w-12 h-12 mx-auto text-muted-foreground" />
          <div className="space-y-2">
            <div className="text-lg font-medium">查看提交历史</div>
            <div className="text-sm text-muted-foreground">
              在左侧历史面板中选择一个提交，这里将显示：
            </div>
            <div className="text-xs text-muted-foreground space-y-1">
              <div>• 提交的详细信息和统计数据</div>
              <div>• 修改的文件列表</div>
              <div>• 文件的历史版本内容</div>
              <div>• 文件恢复和编辑功能</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* 提交信息头部 */}
      <div className="p-4 border-b border-border">
        <div className="space-y-3">
          <div className="text-lg font-medium">
            {commit.commit.message}
          </div>
          
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <div className="flex items-center space-x-1">
              <User className="w-4 h-4" />
              <span>{commit.author?.login || commit.commit.author.name}</span>
            </div>
            <div className="flex items-center space-x-1">
              <Calendar className="w-4 h-4" />
              <span>{formatDate(commit.commit.author.date)}</span>
            </div>
            <div className="flex items-center space-x-1">
              <GitCommit className="w-4 h-4" />
              <span className="font-mono">{commit.sha.substring(0, 7)}</span>
            </div>
          </div>

          {stats && (
            <div className="flex items-center space-x-4 text-sm">
              <span className="text-green-600 font-medium">+{stats.additions}</span>
              <span className="text-red-600 font-medium">-{stats.deletions}</span>
              <span className="text-muted-foreground">
                {files.length} 个文件被修改
              </span>
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 flex">
        {/* 文件列表 */}
        <div className="w-1/3 border-r border-border">
          <div className="p-3 border-b border-border">
            <h4 className="text-sm font-medium">修改的文件 ({files.length})</h4>
          </div>
          <ScrollArea className="h-full">
            <div className="p-2">
              {files.map((file: any, index: number) => (
                <div
                  key={index}
                  className={`p-2 text-sm border rounded cursor-pointer transition-colors hover:bg-muted/50 mb-1 ${
                    selectedFile === file.filename ? 'bg-muted border-primary' : ''
                  }`}
                  onClick={() => handleFileSelect(file.filename)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 min-w-0 flex-1">
                      <FileText className="w-4 h-4 flex-shrink-0" />
                      <span className="truncate" title={file.filename}>
                        {file.filename}
                      </span>
                    </div>
                    <div className="flex items-center space-x-1 text-xs">
                      {file.additions > 0 && (
                        <span className="text-green-600">+{file.additions}</span>
                      )}
                      {file.deletions > 0 && (
                        <span className="text-red-600">-{file.deletions}</span>
                      )}
                    </div>
                  </div>
                  <div className="text-xs text-muted-foreground mt-1">
                    {file.status}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>

        {/* 文件内容 */}
        <div className="flex-1">
          {selectedFile ? (
            <div className="h-full flex flex-col">
              <div className="p-3 border-b border-border">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <FileText className="w-4 h-4" />
                    <span className="text-sm font-medium">{selectedFile}</span>
                  </div>
                  {fileContent && fileContent !== '无法获取文件内容' && (
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRestoreFile(selectedFile, fileContent)}
                        className="h-7 text-xs bg-orange-50 text-orange-600 border-orange-200 hover:bg-orange-100 hover:text-orange-700 dark:bg-orange-950/50 dark:text-orange-400 dark:border-orange-800 dark:hover:bg-orange-900/50"
                      >
                        <Download className="w-3 h-3 mr-1" />
                        恢复此版本
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          if (onFileSelect) {
                            onFileSelect(selectedFile);
                          }
                        }}
                        className="h-7 text-xs"
                      >
                        <Eye className="w-3 h-3 mr-1" />
                        在编辑器中打开
                      </Button>
                    </div>
                  )}
                </div>
              </div>
              
              <ScrollArea className="flex-1">
                <div className="p-4">
                  {loading ? (
                    <div className="text-sm text-muted-foreground">加载中...</div>
                  ) : fileContent ? (
                    <pre className="text-sm whitespace-pre-wrap break-words">
                      {fileContent}
                    </pre>
                  ) : (
                    <div className="text-sm text-muted-foreground">
                      点击左侧文件查看内容
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>
          ) : (
            <div className="h-full flex items-center justify-center">
              <div className="text-center space-y-3">
                <FileText className="w-10 h-10 mx-auto text-muted-foreground" />
                <div className="space-y-1">
                  <div className="text-sm font-medium">选择文件查看内容</div>
                  <div className="text-xs text-muted-foreground">
                    点击左侧文件列表中的任意文件
                  </div>
                  <div className="text-xs text-muted-foreground">
                    查看该文件在此提交时的完整内容
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
