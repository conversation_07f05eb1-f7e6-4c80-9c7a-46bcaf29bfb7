# PR-3 实施疑问 - 长文档性能优化

## 开发人员疑问汇总

针对 PR-3 的长文档性能优化，我需要专家提供具体的技术实施指导，确保达到性能目标。

## 1. CodeMirror 6 源码模式瘦身

### 1.1 basicSetup 替换方案
```typescript
// 当前代码 - 需要替换
import { basicSetup } from 'codemirror';

const state = EditorState.create({
  extensions: [
    basicSetup, // 🔥 需要替换为精简版本
    markdown(),
  ]
});
```

**疑问**：
- basicSetup 包含哪些具体扩展？
- 哪些扩展是必要的，哪些可以移除？
- 精简版本的具体配置代码？

### 1.2 防抖文本持久化
```typescript
// 当前问题代码
EditorView.updateListener.of((update) => {
  if (update.docChanged) {
    const newValue = update.state.doc.toString(); // 🔥 每次都序列化全文
    onChange(newValue);
  }
});
```

**疑问**：
- 如何实现防抖机制？
- update.changes 的具体使用方法？
- 防抖时间设置为多少合适？

### 1.3 精简扩展配置
```typescript
// 需要的精简配置
const minimalExtensions = [
  // 哪些是必要的？
  lineNumbers(),
  bracketMatching(),
  // 还需要哪些？
];
```

**疑问**：
- 行号显示的最小配置？
- 括号匹配的性能影响？
- 语法高亮是否需要保留？

## 2. 滚动同步节流优化

### 2.1 rAF 合帧实现
```typescript
// 当前问题代码
if (update.viewportChanged && onScrollPositionChange) {
  const scrollTop = update.view.scrollDOM.scrollTop;
  onScrollPositionChange(scrollTop); // 🔥 高频回调
}
```

**疑问**：
- requestAnimationFrame 的具体使用方式？
- 如何实现 16ms 节流？
- 合帧的具体实现代码？

### 2.2 节流机制设计
```typescript
// 需要实现的节流逻辑
let rafId: number;
let lastScrollTime = 0;

const throttledScrollHandler = (scrollTop: number) => {
  // 具体的节流实现？
};
```

**疑问**：
- 节流和防抖的选择标准？
- 如何处理快速滚动的情况？
- 内存泄漏的防范措施？

## 3. 大纲改造 - PM-Doc 遍历

### 3.1 PM-Doc 节点遍历
```typescript
// 当前问题代码 - 需要替换
const parseHeadings = (markdown: string): HeadingItem[] => {
  const lines = markdown.split('\n'); // 🔥 逐行正则解析
  // ...
};
```

**疑问**：
- 如何遍历 PM-Doc 的节点？
- heading 节点的类型判断方法？
- 节点位置信息的获取？

### 3.2 新的大纲生成逻辑
```typescript
// 需要实现的新逻辑
const parseHeadingsFromPMDoc = (pmDoc: any): HeadingItem[] => {
  // 如何遍历 PM-Doc？
  // 如何提取标题信息？
  // 如何计算行号？
};
```

**疑问**：
- PM-Doc 的节点结构？
- 标题级别的获取方法？
- 性能对比数据？

## 4. 渲染虚拟化实现

### 4.1 视口计算
```typescript
// 虚拟化的视口计算
const calculateViewport = (scrollTop: number, containerHeight: number) => {
  // 如何计算可见区域？
  // 如何确定 ±N 块的范围？
  // N=6 的依据是什么？
};
```

**疑问**：
- 视口计算的具体算法？
- 块高度的估算方法？
- 缓冲区大小的优化？

### 4.2 增量补齐机制
```typescript
// 滚动时的增量补齐
const handleScroll = (scrollTop: number) => {
  const visibleRange = calculateViewport(scrollTop, containerHeight);
  
  // 如何判断需要补齐的块？
  // 如何避免重复渲染？
  // 如何处理快速滚动？
};
```

**疑问**：
- 增量补齐的触发条件？
- 渲染队列的管理策略？
- 内存回收的时机？

### 4.3 DOM 节点管理
```typescript
// 虚拟化的 DOM 管理
const manageDOMNodes = (visibleBlocks: Block[]) => {
  // 如何复用 DOM 节点？
  // 如何处理节点的创建和销毁？
  // 如何保持滚动位置？
};
```

**疑问**：
- DOM 节点池的实现？
- 节点复用的策略？
- 滚动位置的精确保持？

## 5. IME 友好处理

### 5.1 输入法事件监听
```typescript
// IME 事件处理
let isComposing = false;

const handleCompositionStart = () => {
  isComposing = true;
  // 如何暂停 Worker 消息？
  // 如何暂停块替换？
};

const handleCompositionEnd = () => {
  isComposing = false;
  // 如何恢复处理？
  // 100-200ms 延迟的实现？
};
```

**疑问**：
- compositionstart/end 的兼容性？
- Worker 消息的暂停机制？
- 合批刷新的具体实现？

### 5.2 中文输入优化
```typescript
// 中文输入的特殊处理
const handleChineseInput = (event: CompositionEvent) => {
  // 如何处理拼音输入？
  // 如何避免渲染闪烁？
  // 如何保持光标位置？
};
```

**疑问**：
- 不同输入法的兼容性？
- 输入过程中的状态管理？
- 性能影响的最小化？

## 6. 性能验收标准

### 6.1 性能测试实现
```typescript
// 10k 行文档的性能测试
const performanceTest = {
  scrollFPS: () => {
    // 如何测量滚动 FPS？
    // 目标：≥55 FPS
  },
  
  modeSwitchTime: () => {
    // 如何测量模式切换时间？
    // 目标：≤150 ms
  },
  
  pasteResponseTime: () => {
    // 如何测量粘贴响应时间？
    // 目标：5k 行 ≤200 ms
  }
};
```

**疑问**：
- FPS 测量的具体方法？
- 性能测试的自动化？
- 测试数据的收集和分析？

### 6.2 性能监控集成
```typescript
// 实时性能监控
const performanceMonitor = {
  trackRenderTime: (startTime: number) => {
    // 如何监控渲染时间？
  },
  
  trackMemoryUsage: () => {
    // 如何监控内存使用？
  },
  
  trackUserInteraction: () => {
    // 如何监控用户交互响应？
  }
};
```

**疑问**：
- 性能数据的上报机制？
- 性能回归的预警？
- 用户端的性能监控？

## 7. 具体实施步骤

### 7.1 优化顺序
1. 先优化 CodeMirror 配置？
2. 再实现滚动节流？
3. 最后实现虚拟化？

**疑问**：
- 优化的优先级排序？
- 每步优化的验证方法？
- 如何避免优化冲突？

### 7.2 测试策略
```typescript
// 性能测试用例
const testCases = [
  { name: '1k行文档', lines: 1000 },
  { name: '5k行文档', lines: 5000 },
  { name: '10k行文档', lines: 10000 },
];
```

**疑问**：
- 测试文档的生成方法？
- 测试环境的标准化？
- 性能基准的建立？

## 需要专家提供的具体代码

1. **CodeMirror 精简配置的完整代码**
2. **滚动节流的 rAF 实现**
3. **PM-Doc 遍历的具体方法**
4. **虚拟化渲染的完整实现**
5. **IME 处理的最佳实践代码**
6. **性能测试和监控的工具代码**

---

**请专家提供以上疑问的具体实施代码，特别是虚拟化渲染和性能监控的实现细节。**

**文档版本**: aug-4  
**生成时间**: 2025-01-08  
**专题**: PR-3 长文档性能优化疑问
