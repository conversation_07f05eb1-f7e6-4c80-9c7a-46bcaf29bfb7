import { useState } from 'react';
import { useSession, SessionProvider } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

function TestGitHubAPIContent() {
  const { data: session } = useSession();
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testAPI = async (testType: string) => {
    if (!session?.accessToken) {
      setResult('错误: 需要GitHub登录');
      return;
    }

    setLoading(true);
    setResult('测试中...');

    try {
      const { GitHubService } = await import('@/lib/github');
      const githubService = new GitHubService(session.accessToken);

      switch (testType) {
        case 'user':
          const user = await githubService.getUser();
          setResult(`用户信息获取成功:\n${JSON.stringify(user, null, 2)}`);
          break;

        case 'repos':
          const repos = await githubService.getUserRepos();
          setResult(`仓库列表获取成功 (${repos.length}个):\n${repos.map(r => `- ${r.full_name}`).join('\n')}`);
          break;

        case 'test-repo':
          // 测试访问特定仓库
          const repoContents = await githubService.getRepoContents('zlaogui', 'doc');
          setResult(`仓库内容获取成功:\n${JSON.stringify(repoContents, null, 2)}`);
          break;

        case 'test-file':
          // 测试读取文件
          try {
            const fileContent = await githubService.getFileContent('zlaogui', 'doc', 'README.md');
            setResult(`文件内容读取成功:\n${fileContent.substring(0, 200)}...`);
          } catch (error) {
            setResult(`文件读取失败: ${error}`);
          }
          break;

        case 'test-create':
          // 测试创建文件
          try {
            const testContent = `# 测试文件\n\n这是一个API测试文件，创建时间: ${new Date().toISOString()}`;
            const result = await githubService.createOrUpdateFile(
              'zlaogui',
              'doc',
              'api-test.md',
              testContent,
              '通过API测试创建文件'
            );
            setResult(`文件创建成功:\n${JSON.stringify(result, null, 2)}`);
          } catch (error) {
            setResult(`文件创建失败: ${error}`);
          }
          break;

        case 'verify-push':
          // 验证最近的推送
          try {
            const readmeContent = await githubService.getFileContent('zlaogui', 'doc', 'README.md');
            const ceshiContent = await githubService.getFileContent('zlaogui', 'doc', 'ceshi.md');

            setResult(`验证推送结果:\n\nREADME.md 当前内容:\n${readmeContent}\n\nceshi.md 当前内容:\n${ceshiContent}\n\n检查时间: ${new Date().toLocaleString()}`);
          } catch (error) {
            setResult(`验证失败: ${error}`);
          }
          break;

        case 'check-local':
          // 检查本地文件内容
          try {
            const { SimpleGitService } = await import('@/lib/simpleGit');
            const gitService = SimpleGitService.getInstance();

            const localFiles = gitService.getFileList();
            let localContent = `本地文件列表: ${localFiles.join(', ')}\n\n`;

            for (const file of localFiles) {
              const content = gitService.getFileContent(file);
              localContent += `${file} (长度: ${content?.length || 0}):\n${content || '(空内容)'}\n\n---\n\n`;
            }

            setResult(localContent);
          } catch (error) {
            setResult(`检查本地文件失败: ${error}`);
          }
          break;

        case 'sync-test':
          // 测试同步功能
          try {
            const { SimpleGitService } = await import('@/lib/simpleGit');
            const gitService = SimpleGitService.getInstance();

            // 创建测试文件
            const testContent = `# 同步测试\n\n这是一个同步测试文件，创建时间: ${new Date().toLocaleString()}\n\n内容应该能正确推送到GitHub。`;
            gitService.updateFile('sync-test.md', testContent);

            // 验证文件已保存
            const savedContent = gitService.getFileContent('sync-test.md');

            setResult(`同步测试完成:\n\n保存的内容长度: ${savedContent?.length || 0}\n\n内容预览:\n${savedContent || '(无内容)'}`);
          } catch (error) {
            setResult(`同步测试失败: ${error}`);
          }
          break;

        case 'debug-push':
          // 调试推送过程
          try {
            const { SimpleGitService } = await import('@/lib/simpleGit');
            const gitService = SimpleGitService.getInstance();

            const localFiles = gitService.getFileList();
            let debugInfo = `调试推送过程:\n\n本地文件数量: ${localFiles.length}\n\n`;

            for (const filepath of localFiles) {
              const content = gitService.getFileContent(filepath);
              debugInfo += `文件: ${filepath}\n`;
              debugInfo += `内容长度: ${content?.length || 0}\n`;
              debugInfo += `内容为空: ${!content}\n`;
              debugInfo += `内容预览: ${content ? content.substring(0, 100) + '...' : '(无内容)'}\n\n`;

              // 测试推送单个文件
              if (content) {
                try {
                  console.log(`测试推送文件: ${filepath}`);
                  const result = await githubService.createOrUpdateFile(
                    'zlaogui',
                    'doc',
                    filepath,
                    content,
                    `调试推送: ${filepath}`
                  );
                  debugInfo += `推送结果: 成功 (SHA: ${result.content?.sha || 'N/A'})\n\n`;
                } catch (pushError) {
                  debugInfo += `推送结果: 失败 - ${pushError}\n\n`;
                }
              }
            }

            setResult(debugInfo);
          } catch (error) {
            setResult(`调试推送失败: ${error}`);
          }
          break;

        default:
          setResult('未知测试类型');
      }
    } catch (error) {
      console.error('API测试失败:', error);
      setResult(`测试失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  if (!session) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle>GitHub API 测试</CardTitle>
          </CardHeader>
          <CardContent>
            <p>请先登录GitHub账户</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>GitHub API 测试工具</CardTitle>
          <p className="text-sm text-muted-foreground">
            当前用户: {session.user.githubLogin || session.user.name}
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            <Button 
              onClick={() => testAPI('user')} 
              disabled={loading}
              variant="outline"
            >
              测试用户信息
            </Button>
            <Button 
              onClick={() => testAPI('repos')} 
              disabled={loading}
              variant="outline"
            >
              测试仓库列表
            </Button>
            <Button 
              onClick={() => testAPI('test-repo')} 
              disabled={loading}
              variant="outline"
            >
              测试仓库内容
            </Button>
            <Button 
              onClick={() => testAPI('test-file')} 
              disabled={loading}
              variant="outline"
            >
              测试文件读取
            </Button>
            <Button
              onClick={() => testAPI('test-create')}
              disabled={loading}
              variant="destructive"
            >
              测试文件创建
            </Button>
            <Button
              onClick={() => testAPI('verify-push')}
              disabled={loading}
              variant="outline"
            >
              验证推送结果
            </Button>
            <Button
              onClick={() => testAPI('check-local')}
              disabled={loading}
              variant="outline"
            >
              检查本地文件
            </Button>
            <Button
              onClick={() => testAPI('sync-test')}
              disabled={loading}
              variant="secondary"
            >
              测试同步功能
            </Button>
            <Button
              onClick={() => testAPI('debug-push')}
              disabled={loading}
              variant="destructive"
            >
              调试推送过程
            </Button>
          </div>

          <div className="mt-4">
            <h3 className="font-semibold mb-2">测试结果:</h3>
            <pre className="bg-muted p-4 rounded-md text-sm overflow-auto max-h-96 whitespace-pre-wrap">
              {result || '点击上方按钮开始测试...'}
            </pre>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default function TestGitHubAPI() {
  return (
    <SessionProvider>
      <TestGitHubAPIContent />
    </SessionProvider>
  );
}
