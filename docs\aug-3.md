# PR-2 实施疑问 - Web Worker 渲染架构

## 开发人员疑问汇总

针对 PR-2 的 Web Worker 渲染内核和微信导出功能，我需要专家提供具体的技术实施指导。

## 1. 渲染内核架构

### 1.1 目录结构疑问
```
/editor/render/
├── tokens.ts      # 品牌绿主题 Tokens
├── blocks.ts      # HTML 片段生成
├── whitelist.ts   # 微信友好标签
└── inline-style.ts # 行内样式工具
```

**疑问**：
- 这个目录应该放在 `components/editor/render/` 还是 `lib/render/`？
- 是否需要额外的配置文件？
- 如何组织样式常量？

### 1.2 tokens.ts 结构设计
```typescript
// tokens.ts - 需要具体的 Token 定义
export const brandTokens = {
  colors: {
    primary: '#某个绿色值？',
    // 需要完整的颜色体系
  },
  typography: {
    // 字体大小、行高等
  },
  spacing: {
    // 间距体系
  }
};
```

**疑问**：
- 品牌绿的具体色值？
- 微信公众号的样式限制？
- Token 的命名规范？

### 1.3 blocks.ts 实现细节
```typescript
// blocks.ts - 需要具体的实现代码
export const renderParagraph = (content: string, tokens: any) => {
  // 如何生成内联样式的 HTML？
  return `<p style="...">${content}</p>`;
};

export const renderHeading = (level: number, content: string, tokens: any) => {
  // 标题的样式规则？
};

export const renderCodeBlock = (code: string, language: string, tokens: any) => {
  // 代码块的高亮如何处理？
  // 微信支持的代码样式？
};
```

**疑问**：
- 每种块类型的具体样式规则？
- 如何处理嵌套结构？
- 图片和链接的处理方式？

## 2. Web Worker 实现

### 2.1 Worker 文件结构
```typescript
// /editor/workers/render.worker.ts
// 如何正确设置 Worker？
self.onmessage = function(e) {
  const { type, data } = e.data;
  
  switch (type) {
    case 'RENDER_BLOCKS':
      // 具体实现？
      break;
    case 'RENDER_FULL_DOCUMENT':
      // 具体实现？
      break;
  }
};
```

**疑问**：
- Worker 的消息协议设计？
- 如何处理 PM-Doc 的序列化？
- 错误处理和超时机制？

### 2.2 主线程与 Worker 通信
```typescript
// 主线程如何与 Worker 通信？
const worker = new Worker('/editor/workers/render.worker.ts');

worker.postMessage({
  type: 'RENDER_BLOCKS',
  data: {
    blocks: pmDocBlocks, // 如何提取 PM-Doc 的块？
    tokens: brandTokens
  }
});

worker.onmessage = (e) => {
  const { type, result } = e.data;
  // 如何处理返回结果？
};
```

**疑问**：
- 如何从 PM-Doc 提取块数据？
- 消息队列的管理策略？
- 内存泄漏的防范？

### 2.3 按块增量渲染
```typescript
// 如何实现按块渲染？
const renderBlocks = (blocks: PMBlock[]) => {
  const results = [];
  
  for (const block of blocks) {
    // 如何判断块类型？
    // 如何生成对应的 HTML？
    // 如何处理任务切片？
  }
  
  return results;
};
```

**疑问**：
- PM-Doc 块的类型判断方法？
- 任务切片的具体实现？
- 渲染优先级的处理？

## 3. 微信导出功能

### 3.1 HTML 生成规则
```typescript
// serialize/wechat-html.ts
export const generateWeChatHTML = (pmDoc: any) => {
  // 如何遍历 PM-Doc？
  // 如何确保微信兼容性？
  
  return `
    <div style="...">
      <!-- 具体的 HTML 结构？ -->
    </div>
  `;
};
```

**疑问**：
- 微信公众号的 HTML 限制？
- 禁用的标签和属性列表？
- 样式的内联化规则？

### 3.2 复制功能实现
```typescript
// "复制到公众号"按钮的实现
const copyToWeChat = async () => {
  const html = generateWeChatHTML(pmDoc);
  
  // 如何复制 HTML 到剪贴板？
  await navigator.clipboard.write([
    new ClipboardItem({
      'text/html': new Blob([html], { type: 'text/html' })
    })
  ]);
};
```

**疑问**：
- 剪贴板 API 的兼容性处理？
- 复制失败时的降级方案？
- 用户反馈的实现？

## 4. 现有组件替换

### 4.1 Preview/Split 模式修改
```typescript
// 如何修改 PreviewMode.tsx？
import { useDocContext } from './doc/DocContext';
import { useWorkerRenderer } from './hooks/useWorkerRenderer';

export default function PreviewMode() {
  const { getPmdoc } = useDocContext();
  const { renderHTML } = useWorkerRenderer();
  
  // 如何触发渲染？
  // 如何显示渲染结果？
}
```

**疑问**：
- useWorkerRenderer Hook 的实现？
- 渲染状态的管理？
- 加载和错误状态的处理？

### 4.2 旧逻辑的移除
```typescript
// 需要删除哪些具体的代码？
// MarkdownRenderer.tsx 中的哪些方法？
// NativeDOMRenderer.tsx 中的哪些逻辑？
```

**疑问**：
- 删除的具体范围？
- 如何确保不影响其他功能？
- 是否需要保留备份？

## 5. 性能优化细节

### 5.1 按块 diff 更新
```typescript
// 如何实现按块 diff？
const updateBlocks = (oldBlocks: HTMLElement[], newBlocks: string[]) => {
  // 具体的 diff 算法？
  // 如何最小化 DOM 操作？
};
```

**疑问**：
- Diff 算法的选择？
- 块的唯一标识策略？
- 性能监控指标？

### 5.2 内存管理
```typescript
// Worker 的内存管理
const cleanupWorker = () => {
  // 如何清理 Worker 资源？
  // 如何避免内存泄漏？
};
```

**疑问**：
- Worker 的生命周期管理？
- 内存使用的监控？
- 垃圾回收的触发时机？

## 6. 验收标准实现

### 6.1 微信兼容性测试
**疑问**：
- 如何自动化测试微信兼容性？
- 样式回归的检测方法？
- 测试用例的设计？

### 6.2 性能指标监控
**疑问**：
- 如何测量渲染性能？
- 性能数据的收集方式？
- 性能回归的预警机制？

## 需要专家提供的具体代码

1. **完整的 tokens.ts 和 blocks.ts 实现**
2. **render.worker.ts 的消息协议和渲染逻辑**
3. **wechat-html.ts 的完整实现**
4. **useWorkerRenderer Hook 的实现**
5. **性能监控和测试的具体代码**

---

**请专家提供以上疑问的具体实施代码，特别是微信 HTML 的生成规则和 Worker 的实现细节。**

**文档版本**: aug-3  
**生成时间**: 2025-01-08  
**专题**: PR-2 Web Worker 渲染架构疑问
