// 简化的Git服务，专注于本地操作和基本功能
export interface GitConfig {
  name: string;
  email: string;
  token?: string;
}

export interface RepoInfo {
  owner: string;
  repo: string;
  branch?: string;
}

export interface CommitInfo {
  id: string;
  message: string;
  author: string;
  date: string;
  timestamp: number;
  pushed?: boolean; // 标识是否已推送到远程仓库
}

export interface BranchInfo {
  name: string;
  isActive: boolean;
  lastCommit?: {
    id: string;
    message: string;
    author: string;
    date: string;
  };
  isRemote?: boolean;
}

export interface FileStatus {
  filepath: string;
  status: 'unmodified' | 'modified' | 'staged' | 'new' | 'deleted';
  staged?: boolean; // 是否已暂存
}

// 简化的Git服务类
export class SimpleGitService {
  private static instance: SimpleGitService;
  private config: GitConfig | null = null;
  private currentRepo: RepoInfo | null = null;
  private files: Map<string, string> = new Map(); // 当前工作区文件
  private baselineFiles: Map<string, string> = new Map(); // 基线版本（上次同步的版本）
  private stagedFiles: Set<string> = new Set(); // 暂存区文件
  private commits: CommitInfo[] = [];
  private committedFiles: Set<string> = new Set(); // 已提交但未推送的文件
  
  // 分支管理
  private currentBranch: string = 'main';
  private branches: Map<string, BranchInfo> = new Map();

  private constructor() {
    this.loadFromStorage();
    this.initializeDefaultBranch();
  }

  private initializeDefaultBranch(): void {
    if (this.branches.size === 0) {
      this.branches.set('main', {
        name: 'main',
        isActive: true,
        isRemote: false
      });
    }
  }

  static getInstance(): SimpleGitService {
    if (!SimpleGitService.instance) {
      SimpleGitService.instance = new SimpleGitService();
    }
    return SimpleGitService.instance;
  }

  // 设置Git配置
  setConfig(config: GitConfig): void {
    this.config = config;
    this.saveToStorage();
  }

  // 获取Git配置
  getConfig(): GitConfig | null {
    return this.config;
  }

  // 连接仓库（模拟）
  async connectRepo(repoInfo: RepoInfo, skipSampleFiles?: boolean): Promise<void> {
    this.currentRepo = repoInfo;
    this.saveToStorage();

    // 只有在非GitHub仓库连接时才加载示例文件
    if (!skipSampleFiles && this.files.size === 0) {
      console.log('🔧 加载本地示例文件...');
      const sampleFiles = {
        'README.md': '# 项目文档\n\n欢迎使用Markdown编辑器！\n\n## 功能特性\n\n- 实时预览\n- Git集成\n- 多平台导出\n- GitHub OAuth登录\n\n## 快速开始\n\n1. 点击左侧Git面板中的文件开始编辑\n2. 使用右上角的GitHub登录功能\n3. 选择您的GitHub仓库进行协作\n\n开始您的Markdown编写之旅吧！',
        'docs/guide.md': '# 使用指南\n\n这里是详细的使用说明。\n\n## 快速开始\n\n1. 配置Git信息\n2. 连接仓库\n3. 开始编辑\n\n## 编辑功能\n\n- 支持标准Markdown语法\n- 实时预览\n- 语法高亮\n- 自动保存\n\n## Git工作流\n\n- 查看文件状态\n- 提交更改\n- 推送到远程仓库\n- 拉取最新更改',
        'CHANGELOG.md': '# 更新日志\n\n## v1.1.0 - 2024-07-30\n\n### 新增功能\n- GitHub OAuth第三方登录\n- 真实GitHub仓库集成\n- 文件点击编辑功能\n- 创建新文件功能\n\n## v1.0.0\n\n- 初始版本发布\n- 添加Git工作流支持\n- Markdown编辑器基础功能'
      };
      
      Object.entries(sampleFiles).forEach(([path, content]) => {
        this.files.set(path, content);
        this.baselineFiles.set(path, content); // 设置为基线版本
      });
      this.saveToStorage();
    } else if (skipSampleFiles) {
      console.log('🔧 跳过示例文件加载，等待GitHub文件拉取...');
    }
  }

  // 初始化新仓库
  async initRepo(): Promise<void> {
    this.currentRepo = {
      owner: 'local',
      repo: 'new-project',
      branch: 'main'
    };
    this.files.clear();
    this.baselineFiles.clear();
    this.stagedFiles.clear();
    this.committedFiles.clear();
    this.commits = [];
    this.saveToStorage();
  }

  // 写入文件
  async writeFile(filepath: string, content: string): Promise<void> {
    this.files.set(filepath, content);
    this.saveToStorage();
  }

  // 读取文件
  async readFile(filepath: string): Promise<string> {
    const content = this.files.get(filepath);
    if (content === undefined) {
      throw new Error(`文件不存在: ${filepath}`);
    }
    return content;
  }

  // 获取文件列表
  getFiles(): string[] {
    return Array.from(this.files.keys());
  }

  // 获取文件状态 - 实现真正的Git状态检查
  getFileStatus(): FileStatus[] {
    const statuses: FileStatus[] = [];
    const allFiles = new Set([...Array.from(this.files.keys()), ...Array.from(this.baselineFiles.keys())]);
    
    console.log('=== Git文件状态检查 ===');
    console.log(`当前文件数量: ${this.files.size}`);
    console.log(`基线文件数量: ${this.baselineFiles.size}`);
    console.log(`暂存文件数量: ${this.stagedFiles.size}`);
    console.log('当前文件列表:', Array.from(this.files.keys()));
    console.log('基线文件列表:', Array.from(this.baselineFiles.keys()));
    
    Array.from(allFiles).forEach(filepath => {
      const currentContent = this.files.get(filepath);
      const baselineContent = this.baselineFiles.get(filepath);
      const isStaged = this.stagedFiles.has(filepath);
      
      console.log(`\n检查文件: ${filepath}`);
      console.log(`- 当前内容长度: ${currentContent?.length || 0}`);
      console.log(`- 基线内容长度: ${baselineContent?.length || 0}`);
      console.log(`- 是否已暂存: ${isStaged}`);
      console.log(`- 内容是否相同: ${currentContent === baselineContent}`);
      
      // 添加内容预览对比
      if (currentContent !== baselineContent) {
        console.log(`- 当前内容预览: "${currentContent?.substring(0, 50) || ''}..."`);
        console.log(`- 基线内容预览: "${baselineContent?.substring(0, 50) || ''}..."`);
      }
      
      let status: FileStatus['status'] = 'unmodified';
      
      if (!baselineContent && currentContent) {
        // 新文件
        status = 'new';
        console.log(`- 状态: 新文件`);
      } else if (baselineContent && !currentContent) {
        // 删除的文件
        status = 'deleted';
        console.log(`- 状态: 已删除`);
      } else if (currentContent !== baselineContent) {
        // 修改的文件 - 只有真正修改了才显示
        status = isStaged ? 'staged' : 'modified';
        console.log(`- 状态: ${isStaged ? '已暂存' : '已修改'}`);
      } else {
        // 🔧 文件没有修改 - 不显示在状态列表中
        console.log(`- 状态: 未修改 (即使已暂存也不显示)`);
        return; // 直接跳过，不添加到状态列表
      }
      
      // 添加到状态列表
      statuses.push({
      filepath,
        status,
        staged: isStaged
      });
      console.log(`+ 添加到状态列表: ${status}`);
    });
    
    console.log(`=== 最终状态数量: ${statuses.length} ===\n`);
    return statuses;
  }

  // 获取文件内容
  getFileContent(filepath: string): string | null {
    const content = this.files.get(filepath);
    console.log(`GitService: 获取文件内容 ${filepath}`);
    console.log(`- 文件存在: ${this.files.has(filepath)}`);
    console.log(`- 内容长度: ${content?.length || 0}`);
    console.log(`- 内容类型: ${typeof content}`);
    
    if (content === undefined) {
      console.warn(`文件不存在: ${filepath}`);
      console.log('当前文件列表:', Array.from(this.files.keys()));
      return null;
    }
    
    return content;
  }

  // 更新文件内容
  updateFile(filepath: string, content: string): void {
    console.log(`📝 GitService: 更新文件 ${filepath}`);
    console.log(`- 新内容长度: ${content.length}`);
    console.log(`- 更新前是否存在: ${this.files.has(filepath)}`);
    console.log(`- 更新前文件数量: ${this.files.size}`);
    
    // 保存更新前的状态用于验证
    const beforeState = new Map(this.files);
    const beforeKeys = Array.from(this.files.keys());
    console.log(`- 更新前文件列表:`, beforeKeys);
    
    this.files.set(filepath, content);
    this.saveToStorage();
    
    const afterKeys = Array.from(this.files.keys());
    console.log(`- 更新后文件列表:`, afterKeys);
    console.log(`- 更新后内容长度: ${this.files.get(filepath)?.length || 0}`);
    console.log(`- 实际更新的文件: ${filepath}`);
    
    // 验证没有意外修改其他文件
    beforeKeys.forEach(key => {
      if (key !== filepath) {
        const beforeContent = beforeState.get(key) || '';
        const afterContent = this.files.get(key) || '';
        if (beforeContent !== afterContent) {
          console.error(`⚠️ 意外修改了其他文件: ${key}`);
          console.error(`  - 之前长度: ${beforeContent.length}, 之后长度: ${afterContent.length}`);
        }
      }
    });
  }

  // 创建新文件
  createFile(filepath: string, content: string = ''): void {
    console.log(`GitService: 创建文件 ${filepath}`);
    console.log(`- 初始内容长度: ${content.length}`);
    
    this.files.set(filepath, content);
    this.baselineFiles.set(filepath, content); // 🔧 修复：同时设置基线版本
    this.saveToStorage();
    
    console.log(`- 保存后内容长度: ${this.files.get(filepath)?.length || 0}`);
    console.log(`- 基线版本长度: ${this.baselineFiles.get(filepath)?.length || 0}`);
  }

  // 删除文件
  deleteFile(filepath: string): void {
    this.files.delete(filepath);
    this.stagedFiles.delete(filepath);
    this.saveToStorage();
  }

  // 重命名文件
  renameFile(oldPath: string, newPath: string): void {
    console.log(`GitService: 重命名文件 ${oldPath} -> ${newPath}`);
    
    // 检查新路径是否已存在
    if (this.files.has(newPath)) {
      throw new Error(`文件 ${newPath} 已存在`);
    }
    
    // 检查旧文件是否存在
    if (!this.files.has(oldPath)) {
      throw new Error(`文件 ${oldPath} 不存在`);
    }
    
    // 获取文件内容和状态
    const content = this.files.get(oldPath) || '';
    const wasStaged = this.stagedFiles.has(oldPath);
    const baselineContent = this.baselineFiles.get(oldPath);
    
    // 在新路径创建文件
    this.files.set(newPath, content);
    
    // 如果有基线版本，也要更新
    if (baselineContent !== undefined) {
      this.baselineFiles.set(newPath, baselineContent);
      this.baselineFiles.delete(oldPath);
    }
    
    // 如果文件在暂存区，更新暂存状态
    if (wasStaged) {
      this.stagedFiles.add(newPath);
      this.stagedFiles.delete(oldPath);
    }
    
    // 删除旧文件
    this.files.delete(oldPath);
    
    this.saveToStorage();
    console.log(`文件重命名成功: ${oldPath} -> ${newPath}`);
  }

  // 获取所有文件列表
  getFileList(): string[] {
    return Array.from(this.files.keys());
  }

  // 暂存文件 (git add)
  stageFile(filepath: string): void {
    if (this.files.has(filepath)) {
      const currentContent = this.files.get(filepath);
      const baselineContent = this.baselineFiles.get(filepath);
      
      // 🔧 只有真正修改过的文件才能暂存
      if (currentContent !== baselineContent) {
        this.stagedFiles.add(filepath);
        this.saveToStorage();
        console.log(`文件已暂存: ${filepath}`);
      } else {
        console.log(`⚠️ 文件未修改，无需暂存: ${filepath}`);
      }
    } else {
      console.log(`⚠️ 文件不存在，无法暂存: ${filepath}`);
    }
  }

  // 取消暂存文件 (git reset)
  unstageFile(filepath: string): void {
    this.stagedFiles.delete(filepath);
    this.saveToStorage();
    console.log(`文件已取消暂存: ${filepath}`);
  }

  // 暂存所有更改
  stageAllChanges(): void {
    const statuses = this.getFileStatus();
    for (const status of statuses) {
      if (status.status === 'modified' || status.status === 'new') {
        this.stagedFiles.add(status.filepath);
      }
    }
    this.saveToStorage();
    console.log(`已暂存 ${this.stagedFiles.size} 个文件`);
  }

  // 获取已暂存的文件
  getStagedFiles(): string[] {
    return Array.from(this.stagedFiles);
  }

  // 清空所有文件
  clearFiles(): void {
    this.files.clear();
    this.baselineFiles.clear();
    this.stagedFiles.clear();
    this.committedFiles.clear();
    this.saveToStorage();
  }

  // 提交更改
  async commit(message: string): Promise<void> {
    if (this.stagedFiles.size === 0) {
      throw new Error('没有暂存的文件需要提交');
    }

    if (!this.config) {
      throw new Error('请先设置Git配置');
    }

    const commitInfo: CommitInfo = {
      id: Math.random().toString(36).substr(2, 9),
      message,
      author: this.config.name,
      date: new Date().toLocaleString('zh-CN'),
      timestamp: Date.now()
    };

    this.commits.push(commitInfo);

    // 将暂存的文件移到已提交文件
    this.stagedFiles.forEach(filepath => {
      this.committedFiles.add(filepath);
    });

    // 清空暂存区
    this.stagedFiles.clear();

    // 更新当前分支的最后提交信息
    this.updateBranchLastCommit(this.currentBranch, commitInfo);

    this.saveToStorage();
    console.log(`提交成功: ${message}`);
  }

  // 获取已提交但未推送的文件
  getCommittedFiles(): Set<string> {
    return this.committedFiles;
  }

  // 获取最新的提交信息
  getLatestCommitMessage(): string {
    if (this.commits.length > 0) {
      return this.commits[0].message;
    }
    return '更新文件';
  }

  // 标记文件为已推送（更新基线版本）
  markAsPushed(filepaths: string[]): void {
    for (const filepath of filepaths) {
      const content = this.files.get(filepath);
      if (content !== undefined) {
        this.baselineFiles.set(filepath, content);
      }
      this.committedFiles.delete(filepath);
    }
    this.saveToStorage();
    console.log(`标记 ${filepaths.length} 个文件为已推送`);
  }

  // 标记提交为已推送
  markCommitsAsPushed(): void {
    this.commits = this.commits.map(commit => ({
      ...commit,
      pushed: true
    }));
    this.saveToStorage();
  }

  // 获取未推送的提交
  getUnpushedCommits(): CommitInfo[] {
    return this.commits.filter(commit => !commit.pushed);
  }

  // === 分支管理 ===
  
  // 获取当前分支
  getCurrentBranch(): string {
    return this.currentBranch;
  }

  // 获取所有分支
  getBranches(): BranchInfo[] {
    const branchArray = Array.from(this.branches.values());
    return branchArray.sort((a, b) => {
      // 当前分支排在最前面
      if (a.isActive && !b.isActive) return -1;
      if (!a.isActive && b.isActive) return 1;
      // 然后按名称排序
      return a.name.localeCompare(b.name);
    });
  }

  // 创建新分支
  createBranch(branchName: string): boolean {
    if (this.branches.has(branchName)) {
      console.warn(`分支 ${branchName} 已存在`);
      return false;
    }

    // 创建新分支，基于当前分支
    const currentBranchInfo = this.branches.get(this.currentBranch);
    this.branches.set(branchName, {
      name: branchName,
      isActive: false,
      isRemote: false,
      lastCommit: currentBranchInfo?.lastCommit
    });

    this.saveToStorage();
    console.log(`✅ 已创建分支: ${branchName}`);
    return true;
  }

  // 切换分支
  switchBranch(branchName: string): boolean {
    if (!this.branches.has(branchName)) {
      console.warn(`分支 ${branchName} 不存在`);
      return false;
    }

    // 检查是否有未提交的更改
    const fileStatus = this.getFileStatus();
    const hasUncommittedChanges = fileStatus.some(f => f.status === 'modified' || f.status === 'new' || f.status === 'staged');
    
    if (hasUncommittedChanges) {
      console.warn(`无法切换分支：存在未提交的更改`);
      return false;
    }

    // 将当前分支设为非活跃
    const currentBranchInfo = this.branches.get(this.currentBranch);
    if (currentBranchInfo) {
      currentBranchInfo.isActive = false;
      this.branches.set(this.currentBranch, currentBranchInfo);
    }

    // 设置新的当前分支
    this.currentBranch = branchName;
    const newBranchInfo = this.branches.get(branchName)!;
    newBranchInfo.isActive = true;
    this.branches.set(branchName, newBranchInfo);

    // 清空未提交的状态（因为切换分支了）
    this.stagedFiles.clear();
    this.committedFiles.clear();

    this.saveToStorage();
    console.log(`✅ 已切换到分支: ${branchName}`);
    return true;
  }

  // 删除分支
  deleteBranch(branchName: string): boolean {
    if (!this.branches.has(branchName)) {
      console.warn(`分支 ${branchName} 不存在`);
      return false;
    }

    if (branchName === this.currentBranch) {
      console.warn(`无法删除当前活跃分支: ${branchName}`);
      return false;
    }

    if (branchName === 'main' || branchName === 'master') {
      console.warn(`无法删除主分支: ${branchName}`);
      return false;
    }

    this.branches.delete(branchName);
    this.saveToStorage();
    console.log(`✅ 已删除分支: ${branchName}`);
    return true;
  }

  // 更新分支的最后提交信息
  private updateBranchLastCommit(branchName: string, commit: CommitInfo): void {
    const branchInfo = this.branches.get(branchName);
    if (branchInfo) {
      branchInfo.lastCommit = {
        id: commit.id,
        message: commit.message,
        author: commit.author,
        date: commit.date
      };
      this.branches.set(branchName, branchInfo);
    }
  }

  // 从远程同步基线版本（拉取后调用）
  updateBaseline(files: Map<string, string>): void {
    this.baselineFiles.clear();
    Array.from(files.entries()).forEach(([filepath, content]) => {
      this.baselineFiles.set(filepath, content);
    });
    this.committedFiles.clear(); // 清空已提交文件，因为远程已经是最新的
    this.saveToStorage();
    console.log(`更新基线版本，包含 ${files.size} 个文件`);
  }

  // 获取提交历史
  getCommitHistory(): CommitInfo[] {
    return this.commits;
  }

  // 获取当前仓库信息
  getCurrentRepo(): RepoInfo | null {
    return this.currentRepo;
  }

  // 模拟推送（显示成功消息）
  async push(): Promise<void> {
    if (!this.currentRepo) {
      throw new Error('没有连接的仓库');
    }
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log(`推送到 ${this.currentRepo.owner}/${this.currentRepo.repo} 成功`);
  }

  // 模拟拉取（显示成功消息）
  async pull(): Promise<void> {
    if (!this.currentRepo) {
      throw new Error('没有连接的仓库');
    }
    
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    console.log(`从 ${this.currentRepo.owner}/${this.currentRepo.repo} 拉取成功`);
  }

  // 保存到本地存储
  private saveToStorage(): void {
    if (typeof window === 'undefined') return;

    const data = {
      config: this.config,
      currentRepo: this.currentRepo,
      files: Array.from(this.files.entries()),
      baselineFiles: Array.from(this.baselineFiles.entries()),
      stagedFiles: Array.from(this.stagedFiles),
      commits: this.commits,
      committedFiles: Array.from(this.committedFiles),
      branches: Array.from(this.branches.entries())
    };

    localStorage.setItem('simple-git-data', JSON.stringify(data));
  }

  // 从本地存储加载
  private loadFromStorage(): void {
    if (typeof window === 'undefined') return;

    try {
      const saved = localStorage.getItem('simple-git-data');
      if (saved) {
        const data = JSON.parse(saved);
        this.config = data.config || null;
        this.currentRepo = data.currentRepo || null;
        this.files = new Map(data.files || []);
        this.baselineFiles = new Map(data.baselineFiles || []);
        this.stagedFiles = new Set(data.stagedFiles || []);
        this.commits = data.commits || [];
        this.committedFiles = new Set(data.committedFiles || []);
        this.branches = new Map(data.branches || []);
      }
    } catch (error) {
      console.error('加载Git数据失败:', error);
    }
  }

  // 清除所有数据
  clear(): void {
    this.config = null;
    this.currentRepo = null;
    this.files.clear();
    this.baselineFiles.clear();
    this.stagedFiles.clear();
    this.committedFiles.clear();
    this.commits = [];
    this.branches.clear(); // 清除分支数据
    
    if (typeof window !== 'undefined') {
      localStorage.removeItem('simple-git-data');
    }
  }

  // 导出数据（用于备份）
  exportData(): string {
    const data = {
      config: this.config,
      currentRepo: this.currentRepo,
      files: Array.from(this.files.entries()),
      baselineFiles: Array.from(this.baselineFiles.entries()),
      stagedFiles: Array.from(this.stagedFiles),
      commits: this.commits,
      committedFiles: Array.from(this.committedFiles),
      branches: Array.from(this.branches.entries()),
      exportDate: new Date().toISOString()
    };
    
    return JSON.stringify(data, null, 2);
  }

  // 导入数据（用于恢复）
  importData(jsonData: string): void {
    try {
      const data = JSON.parse(jsonData);
      this.config = data.config || null;
      this.currentRepo = data.currentRepo || null;
      this.files = new Map(data.files || []);
      this.baselineFiles = new Map(data.baselineFiles || []);
      this.stagedFiles = new Set(data.stagedFiles || []);
      this.commits = data.commits || [];
      this.committedFiles = new Set(data.committedFiles || []);
      this.branches = new Map(data.branches || []);
      this.saveToStorage();
    } catch (error) {
      throw new Error('导入数据格式错误');
    }
  }

  // 修复基线版本（用于修复已存在的文件）
  fixBaseline(): void {
    console.log('🔧 修复基线版本问题...');
    let fixedCount = 0;
    
    // 为所有没有基线版本的文件设置基线版本
    Array.from(this.files.entries()).forEach(([filepath, content]) => {
      if (!this.baselineFiles.has(filepath)) {
        console.log(`- 修复文件: ${filepath}`);
        this.baselineFiles.set(filepath, content);
        fixedCount++;
      }
    });
    
    // 清理已删除文件的基线版本
    Array.from(this.baselineFiles.keys()).forEach(filepath => {
      if (!this.files.has(filepath)) {
        console.log(`- 清理已删除文件的基线: ${filepath}`);
        this.baselineFiles.delete(filepath);
      }
    });
    
    // 🔧 清理无效的暂存状态
    let stagedCleanedCount = 0;
    Array.from(this.stagedFiles).forEach(filepath => {
      const currentContent = this.files.get(filepath);
      const baselineContent = this.baselineFiles.get(filepath);
      
      // 如果文件不存在或者内容完全相同，则清理暂存状态
      if (!currentContent || currentContent === baselineContent) {
        console.log(`- 清理无效暂存状态: ${filepath}`);
        this.stagedFiles.delete(filepath);
        stagedCleanedCount++;
      }
    });
    
    if (fixedCount > 0 || stagedCleanedCount > 0) {
      this.saveToStorage();
      console.log(`✅ 修复了 ${fixedCount} 个文件的基线版本, 清理了 ${stagedCleanedCount} 个无效暂存状态`);
    } else {
      console.log('✅ 基线版本和暂存状态正常，无需修复');
    }
  }

  // 撤销文件修改（恢复到基线版本）
  discardChanges(filepath: string): boolean {
    const baselineContent = this.baselineFiles.get(filepath);
    if (baselineContent !== undefined) {
      // 恢复到基线版本
      this.files.set(filepath, baselineContent);
      // 从暂存区移除（如果在暂存区中）
      this.stagedFiles.delete(filepath);
      this.saveToStorage();
      console.log(`✅ 已撤销文件修改: ${filepath}`);
      return true;
    } else {
      // 如果是新文件，直接删除
      if (this.files.has(filepath)) {
        this.files.delete(filepath);
        this.stagedFiles.delete(filepath);
        this.saveToStorage();
        console.log(`✅ 已删除新文件: ${filepath}`);
        return true;
      }
      console.warn(`⚠️ 无法撤销文件修改: ${filepath} (没有基线版本)`);
      return false;
    }
  }

  // 撤销所有未暂存的修改
  discardAllChanges(): number {
    const fileStatus = this.getFileStatus();
    const unstagedFiles = fileStatus.filter(f => f.status === 'modified' || f.status === 'new');
    
    let discardedCount = 0;
    unstagedFiles.forEach(file => {
      if (this.discardChanges(file.filepath)) {
        discardedCount++;
      }
    });
    
    return discardedCount;
  }
}
