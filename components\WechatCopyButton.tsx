"use client";
export default function WechatCopyButton(){
  const onCopy = async () => {
    const el = document.getElementById("wechat-preview");
    const inner = el?.innerHTML ?? "";
    const html = `<article>${inner}</article>`;
    try{
      await navigator.clipboard.write([
        new (window as any).ClipboardItem({ "text/html": new Blob([html], { type: "text/html" }) })
      ]);
      alert("已复制为 HTML，直接粘贴到公众号。");
    }catch{
      await navigator.clipboard.writeText(inner.replace(/<[^>]+>/g, ""));
      alert("浏览器限制：已复制纯文本。");
    }
  };
  return <button onClick={onCopy} style={{ marginLeft: 12 }}>复制到公众号</button>;
}
