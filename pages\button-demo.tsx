'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Download, Eye, RefreshCw, Settings, Sun, Moon } from 'lucide-react';

export default function ButtonDemo() {
  return (
    <div className="min-h-screen bg-background text-foreground p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold">按钮设计对比</h1>
          <p className="text-muted-foreground">
            重新设计的按钮样式，更符合整体网站的现代化设计风格
          </p>
        </div>

        {/* 原始按钮样式 */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">原始按钮样式</h2>
          <div className="p-6 border border-border rounded-lg bg-card">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                className="text-orange-600 hover:text-orange-700"
              >
                <Download className="w-3 h-3 mr-1" />
                恢复此版本
              </Button>
              <Button
                variant="outline"
                size="sm"
              >
                <Eye className="w-3 h-3 mr-1" />
                在编辑器中打开
              </Button>
            </div>
            <p className="text-sm text-muted-foreground mt-3">
              问题：样式过于简单，缺乏视觉层次，与整体设计风格不够协调
            </p>
          </div>
        </div>

        {/* 重新设计的按钮样式 */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">重新设计的按钮样式（修正版）</h2>
          <div className="p-6 border border-border rounded-lg bg-card">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                className="h-7 text-xs bg-orange-50 text-orange-600 border-orange-200 hover:bg-orange-100 hover:text-orange-700 dark:bg-orange-950/50 dark:text-orange-400 dark:border-orange-800 dark:hover:bg-orange-900/50"
              >
                <Download className="w-3 h-3 mr-1" />
                恢复此版本
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-7 text-xs"
              >
                <Eye className="w-3 h-3 mr-1" />
                在编辑器中打开
              </Button>
            </div>
            <p className="text-sm text-muted-foreground mt-3">
              修正：更紧凑的高度、更柔和的颜色、去除阴影、符合网站简洁风格
            </p>
          </div>
        </div>

        {/* 网站现有按钮样式参考 */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">网站现有按钮样式参考</h2>
          <div className="p-6 border border-border rounded-lg bg-card">
            <div className="flex items-center space-x-2 mb-4">
              <Button variant="default" size="sm" className="h-7 text-xs">
                <Download className="w-3 h-3 mr-1" />
                保存
              </Button>
              <Button variant="default" size="sm" className="h-7 text-xs bg-black text-white hover:bg-gray-800">
                <RefreshCw className="w-3 h-3 mr-1" />
                发布
              </Button>
              <Button variant="outline" size="sm" className="h-7 text-xs">
                <Settings className="w-3 h-3 mr-1" />
                导出
              </Button>
            </div>
            <p className="text-sm text-muted-foreground">
              参考网站顶部工具栏的按钮样式：紧凑、简洁、统一的高度
            </p>
          </div>
        </div>

        {/* 设计改进说明 */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">设计改进说明</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-4 border border-border rounded-lg bg-card">
              <h3 className="font-medium mb-2 text-orange-600 dark:text-orange-400">恢复此版本按钮</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 使用柔和的橙色系，表示重要操作</li>
                <li>• 紧凑的高度（h-7）匹配网站风格</li>
                <li>• 去除阴影，保持简洁</li>
                <li>• 低饱和度颜色，符合整体设计</li>
              </ul>
            </div>
            <div className="p-4 border border-border rounded-lg bg-card">
              <h3 className="font-medium mb-2">在编辑器中打开按钮</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• 使用默认的 outline 样式</li>
                <li>• 保持与网站其他按钮一致的外观</li>
                <li>• 统一的尺寸和字体大小</li>
                <li>• 简洁的交互效果</li>
              </ul>
            </div>
          </div>
        </div>

        {/* 其他按钮样式示例 */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">整体设计系统中的其他按钮</h2>
          <div className="p-6 border border-border rounded-lg bg-card">
            <div className="flex flex-wrap items-center gap-3">
              <Button variant="default" size="sm">
                <Settings className="w-3 h-3 mr-1.5" />
                主要操作
              </Button>
              <Button variant="secondary" size="sm">
                <RefreshCw className="w-3 h-3 mr-1.5" />
                次要操作
              </Button>
              <Button variant="outline" size="sm">
                <Sun className="w-3 h-3 mr-1.5" />
                边框按钮
              </Button>
              <Button variant="ghost" size="sm">
                <Moon className="w-3 h-3 mr-1.5" />
                幽灵按钮
              </Button>
            </div>
            <p className="text-sm text-muted-foreground mt-3">
              保持与现有设计系统的一致性，同时提升视觉效果
            </p>
          </div>
        </div>

        {/* 技术实现 */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold">技术实现特点</h2>
          <div className="p-4 border border-border rounded-lg bg-card">
            <ul className="text-sm space-y-2">
              <li className="flex items-start space-x-2">
                <span className="text-green-500 mt-0.5">✓</span>
                <span>使用 Tailwind CSS 实现，保持与项目技术栈一致</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-green-500 mt-0.5">✓</span>
                <span>基于 shadcn/ui 组件库，确保组件的可维护性</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-green-500 mt-0.5">✓</span>
                <span>完整的深色模式支持，自动适配主题切换</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-green-500 mt-0.5">✓</span>
                <span>语义化的颜色使用，提升用户体验</span>
              </li>
              <li className="flex items-start space-x-2">
                <span className="text-green-500 mt-0.5">✓</span>
                <span>微交互动画，增强界面的现代感</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
