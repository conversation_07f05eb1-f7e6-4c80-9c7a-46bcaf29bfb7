'use client';

import React, { useState, useCallback, useMemo } from 'react';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';

interface LiveRenderModeProps {
  content: string;
  onChange: (content: string) => void;
  className?: string;
}

interface ParagraphData {
  id: string;
  markdown: string;
  html: string;
  isEditing: boolean;
}

// 渲染Markdown为HTML的函数
const renderMarkdown = (markdown: string) => {
  try {
    const processor = unified()
      .use(remarkParse)
      .use(remarkGfm)
      .use(remarkRehype, { allowDangerousHtml: true })
      .use(rehypeRaw)
      .use(rehypeHighlight)
      .use(rehypeStringify);

    const result = processor.processSync(markdown);
    return String(result);
  } catch (error) {
    console.error('Markdown rendering error:', error);
    return '<p>渲染错误</p>';
  }
};

export default function LiveRenderMode({
  content,
  onChange,
  className = ''
}: LiveRenderModeProps) {
  const [editingParagraphId, setEditingParagraphId] = useState<string | null>(null);

  // 监听大纲跳转事件
  React.useEffect(() => {
    const handleScrollToLiveHeading = (event: CustomEvent<{ headingId: string; line: number; headingText: string }>) => {
      const { headingId, line } = event.detail;
      console.log(`LiveRenderMode: 跳转到标题 ${headingId}, 行 ${line}`);
      
      // 尝试通过ID查找对应的元素
      const targetElement = document.querySelector(`#${headingId}`);
      if (targetElement) {
        targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        return;
      }
      
      // 如果找不到ID，尝试通过段落内容查找
      // 计算目标行在哪个段落中
      const lines = content.split('\n');
      let currentLine = 1;
      let targetParagraphIndex = -1;
      
      const contentLines = content.split('\n');
      for (let i = 0; i < contentLines.length; i++) {
        if (i + 1 === line) {
          // 找到对应行，确定属于哪个段落
          let paragraphStart = i;
          // 向上找到段落开始
          while (paragraphStart > 0 && contentLines[paragraphStart - 1].trim() !== '') {
            paragraphStart--;
          }
          
          // 计算这是第几个段落
          let paragraphCount = 0;
          for (let j = 0; j < paragraphStart; j++) {
            if (contentLines[j].trim() === '' && j > 0 && contentLines[j - 1].trim() !== '') {
              paragraphCount++;
            }
          }
          if (paragraphStart === 0 || contentLines[paragraphStart].trim() !== '') {
            targetParagraphIndex = paragraphCount;
          }
          break;
        }
      }
      
      // 滚动到对应段落
      if (targetParagraphIndex >= 0) {
        const paragraphElement = document.querySelector(`[data-paragraph-index="${targetParagraphIndex}"]`);
        if (paragraphElement) {
          paragraphElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    };

    document.addEventListener('scrollToLiveHeading', handleScrollToLiveHeading as EventListener);
    
    return () => {
      document.removeEventListener('scrollToLiveHeading', handleScrollToLiveHeading as EventListener);
    };
  }, [content]);

  // 将内容分割成段落
  const paragraphs = useMemo(() => {
    const lines = content.split('\n');
    const paragraphs: ParagraphData[] = [];
    let currentParagraph = '';
    let paragraphIndex = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // 如果是空行，结束当前段落
      if (line.trim() === '') {
        if (currentParagraph.trim() !== '') {
          paragraphs.push({
            id: `paragraph-${paragraphIndex}`,
            markdown: currentParagraph.trim(),
            html: renderMarkdown(currentParagraph.trim()),
            isEditing: editingParagraphId === `paragraph-${paragraphIndex}`
          });
          paragraphIndex++;
        }
        currentParagraph = '';
      } else {
        currentParagraph += (currentParagraph ? '\n' : '') + line;
      }
    }

    // 处理最后一个段落
    if (currentParagraph.trim() !== '') {
      paragraphs.push({
        id: `paragraph-${paragraphIndex}`,
        markdown: currentParagraph.trim(),
        html: renderMarkdown(currentParagraph.trim()),
        isEditing: editingParagraphId === `paragraph-${paragraphIndex}`
      });
    }

    return paragraphs;
  }, [content, editingParagraphId]);

  // 开始编辑段落
  const startEditing = useCallback((paragraphId: string) => {
    setEditingParagraphId(paragraphId);
  }, []);

  // 完成编辑段落
  const finishEditing = useCallback((paragraphId: string, newMarkdown: string) => {
    const paragraphIndex = parseInt(paragraphId.split('-')[1]);
    const newParagraphs = [...paragraphs];
    
    // 更新段落内容
    if (newParagraphs[paragraphIndex]) {
      newParagraphs[paragraphIndex].markdown = newMarkdown;
    }

    // 重新构建完整内容
    const newContent = newParagraphs.map(p => p.markdown).join('\n\n');
    onChange(newContent);
    setEditingParagraphId(null);
  }, [paragraphs, onChange]);

  // 取消编辑
  const cancelEditing = useCallback(() => {
    setEditingParagraphId(null);
  }, []);

  return (
    <div className={`p-4 space-y-4 ${className}`}>
      {paragraphs.map((paragraph, index) => (
        <ParagraphBlock
          key={paragraph.id}
          paragraph={paragraph}
          paragraphIndex={index}
          onStartEdit={() => startEditing(paragraph.id)}
          onFinishEdit={(newMarkdown) => finishEditing(paragraph.id, newMarkdown)}
          onCancelEdit={cancelEditing}
        />
      ))}
      
      {paragraphs.length === 0 && (
        <div className="text-muted-foreground text-center py-8">
          开始编写您的 Markdown 内容...
        </div>
      )}
    </div>
  );
}

interface ParagraphBlockProps {
  paragraph: ParagraphData;
  paragraphIndex: number;
  onStartEdit: () => void;
  onFinishEdit: (newMarkdown: string) => void;
  onCancelEdit: () => void;
}

function ParagraphBlock({
  paragraph,
  paragraphIndex,
  onStartEdit,
  onFinishEdit,
  onCancelEdit
}: ParagraphBlockProps) {
  const [editingContent, setEditingContent] = useState(paragraph.markdown);

  // 为段落中的标题添加ID
  const processedHtml = React.useMemo(() => {
    let html = paragraph.html;
    // 为标题添加ID，与MarkdownRenderer保持一致
    html = html.replace(
      /<(h[1-6])>([^<]+)<\/h[1-6]>/g,
      (match, tag, text) => {
        const id = text.toLowerCase()
          .replace(/[^\w\u4e00-\u9fa5\s-]/g, '') // 保留中文、英文、数字、空格、连字符
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '');
        
        if (id) {
          return `<${tag} id="${id}">${text}</${tag}>`;
        }
        return match;
      }
    );
    return html;
  }, [paragraph.html]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      e.preventDefault();
      onFinishEdit(editingContent);
    } else if (e.key === 'Escape') {
      e.preventDefault();
      setEditingContent(paragraph.markdown);
      onCancelEdit();
    }
  };

  if (paragraph.isEditing) {
    return (
      <div 
        className="border border-primary rounded-lg p-3 bg-muted/50"
        data-paragraph-index={paragraphIndex}
      >
        <textarea
          value={editingContent}
          onChange={(e) => setEditingContent(e.target.value)}
          onKeyDown={handleKeyDown}
          onBlur={() => onFinishEdit(editingContent)}
          className="w-full h-24 resize-none border-none outline-none bg-transparent font-mono text-sm"
          placeholder="编辑段落内容... (Ctrl+Enter保存, Esc取消)"
          autoFocus
        />
        <div className="text-xs text-muted-foreground mt-2">
          Ctrl+Enter 保存 • Esc 取消
        </div>
      </div>
    );
  }

  return (
    <div
      className="prose prose-sm max-w-none dark:prose-invert cursor-pointer hover:bg-muted/30 rounded-lg p-3 transition-colors"
      onClick={onStartEdit}
      data-paragraph-index={paragraphIndex}
      dangerouslySetInnerHTML={{ __html: processedHtml }}
    />
  );
}
