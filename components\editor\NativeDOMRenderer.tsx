'use client';

import React, { useRef, useEffect, useCallback } from 'react';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';

interface NativeDOMRendererProps {
  content: string;
  className?: string;
  initialScrollPosition?: number;
  onScrollPositionChange?: (position: number) => void;
}

interface ContentSegment {
  id: string;
  content: string;
  hash: string; // 内容哈希，用于检测变化
  element?: HTMLElement;
}

// 计算字符串哈希
const hashString = (str: string): string => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash).toString(36);
};

// 渲染单个段落
const renderSegment = (content: string): string => {
  try {
    const processor = unified()
      .use(remarkParse)
      .use(remarkGfm)
      .use(remarkRehype, { allowDangerousHtml: true })
      .use(rehypeRaw)
      .use(rehypeHighlight)
      .use(rehypeStringify);

    let result = processor.processSync(content);
    let htmlString = String(result);
    
    // 为标题添加ID
    htmlString = htmlString.replace(
      /<(h[1-6])>([^<]+)<\/h[1-6]>/g,
      (match, tag, text) => {
        const id = text.toLowerCase()
          .replace(/[^\w\u4e00-\u9fa5\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '');
        
        if (id) {
          return `<${tag} id="${id}">${text}</${tag}>`;
        }
        return match;
      }
    );
    
    return htmlString;
  } catch (error) {
    console.error('Segment rendering error:', error);
    return '<p>渲染错误</p>';
  }
};

// 分割内容为段落
const splitContent = (content: string): ContentSegment[] => {
  if (!content.trim()) return [];

  const lines = content.split('\n');
  const segments: ContentSegment[] = [];
  let currentSegment = '';
  let segmentIndex = 0;
  let inCodeBlock = false;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    if (line.trim().startsWith('```')) {
      inCodeBlock = !inCodeBlock;
      currentSegment += (currentSegment ? '\n' : '') + line;
      
      if (!inCodeBlock && currentSegment.trim()) {
        const segmentContent = currentSegment.trim();
        segments.push({
          id: `segment-${segmentIndex}`,
          content: segmentContent,
          hash: hashString(segmentContent)
        });
        segmentIndex++;
        currentSegment = '';
      }
      continue;
    }

    if (inCodeBlock) {
      currentSegment += (currentSegment ? '\n' : '') + line;
      continue;
    }

    if (line.trim() === '') {
      if (currentSegment.trim() !== '') {
        const segmentContent = currentSegment.trim();
        segments.push({
          id: `segment-${segmentIndex}`,
          content: segmentContent,
          hash: hashString(segmentContent)
        });
        segmentIndex++;
      }
      currentSegment = '';
    } else {
      currentSegment += (currentSegment ? '\n' : '') + line;
    }
  }

  if (currentSegment.trim() !== '') {
    const segmentContent = currentSegment.trim();
    segments.push({
      id: `segment-${segmentIndex}`,
      content: segmentContent,
      hash: hashString(segmentContent)
    });
  }

  return segments;
};

export default function NativeDOMRenderer({
  content,
  className = '',
  initialScrollPosition,
  onScrollPositionChange
}: NativeDOMRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const segmentsRef = useRef<ContentSegment[]>([]);
  const scrollRestoredRef = useRef(false);

  // 精确增量更新DOM
  const updateDOM = useCallback((newSegments: ContentSegment[]) => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    const oldSegments = segmentsRef.current;
    
    // 保存当前滚动位置
    const currentScrollTop = container.scrollTop;
    
    // 创建新段落映射
    const newSegmentMap = new Map(newSegments.map(seg => [seg.id, seg]));
    const oldSegmentMap = new Map(oldSegments.map(seg => [seg.id, seg]));
    
    // 获取现有DOM元素
    const existingElements = Array.from(container.children) as HTMLElement[];
    const elementMap = new Map<string, HTMLElement>();
    
    existingElements.forEach(el => {
      const segmentId = el.getAttribute('data-segment-id');
      if (segmentId) {
        elementMap.set(segmentId, el);
      }
    });

    // 创建新的DOM结构
    const fragment = document.createDocumentFragment();
    
    newSegments.forEach((segment, index) => {
      const oldSegment = oldSegmentMap.get(segment.id);
      const existingElement = elementMap.get(segment.id);
      
      // 如果内容没有变化且DOM元素存在，直接复用
      if (oldSegment && 
          existingElement && 
          oldSegment.hash === segment.hash) {
        fragment.appendChild(existingElement);
        segment.element = existingElement;
      } else {
        // 创建新元素
        const div = document.createElement('div');
        div.className = 'markdown-segment';
        div.setAttribute('data-segment-id', segment.id);
        div.innerHTML = renderSegment(segment.content);
        fragment.appendChild(div);
        segment.element = div;
      }
    });

    // 一次性替换所有内容
    container.innerHTML = '';
    container.appendChild(fragment);
    
    // 精确恢复滚动位置
    container.scrollTop = currentScrollTop;
    
    // 更新引用
    segmentsRef.current = newSegments;
  }, []);

  // 监听内容变化
  useEffect(() => {
    const newSegments = splitContent(content);
    updateDOM(newSegments);
  }, [content, updateDOM]);

  // 初始滚动位置恢复
  useEffect(() => {
    if (containerRef.current && 
        initialScrollPosition !== undefined && 
        !scrollRestoredRef.current) {
      
      requestAnimationFrame(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = initialScrollPosition;
          scrollRestoredRef.current = true;
        }
      });
    }
  }, [initialScrollPosition]);

  // 滚动事件监听
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !onScrollPositionChange) return;

    const handleScroll = () => {
      onScrollPositionChange(container.scrollTop);
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [onScrollPositionChange]);

  // 监听跳转事件
  useEffect(() => {
    const handleScrollToPreviewHeading = (event: CustomEvent<{ headingId: string }>) => {
      if (containerRef.current) {
        const headingElement = containerRef.current.querySelector(`#${event.detail.headingId}`);
        if (headingElement) {
          headingElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    };

    document.addEventListener('scrollToPreviewHeading', handleScrollToPreviewHeading as EventListener);
    
    return () => {
      document.removeEventListener('scrollToPreviewHeading', handleScrollToPreviewHeading as EventListener);
    };
  }, []);

  return (
    <div
      ref={containerRef}
      className={`prose prose-slate dark:prose-invert max-w-none p-4 overflow-auto h-full ${className}`}
      style={{
        lineHeight: '1.7',
        fontSize: '16px'
      }}
    />
  );
} 