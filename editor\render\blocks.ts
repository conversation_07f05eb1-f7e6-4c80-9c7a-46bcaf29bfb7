import { TOKENS as T } from "./tokens";
import { css, esc } from "./inline-style";

export const renderParagraph = (inner:string) =>
  `<p style="${css({"font-size":`${T.font.base}px`,"line-height":T.line.base,color:T.text,margin:`0 0 ${T.space.p}px 0`})}">${inner}</p>`;

export const renderHeading = (lvl:1|2|3, inner:string) => {
  const fs = lvl===1?T.font.h1:lvl===2?T.font.h2:T.font.h3;
  return `<h${lvl} style="${css({"font-size":`${fs}px`,"line-height":T.line.heading,color:T.heading,margin:"24px 0 12px"})}">${inner}</h${lvl}>`;
};

export const renderBlockquote = (inner:string) =>
  `<blockquote style="${css({"border-left":`4px solid ${T.quoteBar}`,"margin":"12px 0","padding":"6px 12px"})}">${inner}</blockquote>`;

export const renderCodeBlock = (codeHtml:string) =>
  `<pre style="${css({background:T.codeBg,border:`1px solid ${T.codeBorder}`,"border-radius":T.radius.code,padding:"12px",overflow:"auto"})}"><code>${codeHtml}</code></pre>`;

export const renderImage = (src:string, alt?:string) =>
  `<img src="${esc(src)}" alt="${esc(alt??"")}" style="${css({"max-width":"100%",height:"auto","border-radius":T.radius.img})}"/>`;

export const renderList = (ordered:boolean, items:string[]) =>
  ordered ? `<ol>${items.map(i=>`<li>${i}</li>`).join("")}</ol>`
          : `<ul>${items.map(i=>`<li>${i}</li>`).join("")}</ul>`;

export const renderTable = (thead:string, tbody:string) =>
  `<table style="${css({width:"100%","border-collapse":"collapse","table-layout":"fixed",border:`1px solid ${T.tableBorder}`})}">${thead}${tbody}</table>`;
