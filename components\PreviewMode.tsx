"use client";
import { useEffect, useRef } from "react";
import { useDocCtx } from "./editor/doc/DocContext";


export default function PreviewMode(){
  const { getPmdoc, subscribe } = useDocCtx();
  const containerRef = useRef<HTMLDivElement>(null);
  const workerRef = useRef<Worker | null>(null);

  // 建立 Worker
  useEffect(()=>{
    const worker = new Worker(
      /* webpackChunkName: "wechat-render" */
      new URL("./wechatRender.worker.ts", import.meta.url)
    );
    workerRef.current = worker;
    const onMsg = (e: MessageEvent<any>) => {
      if (e.data?.type === "render:full:ok" && containerRef.current) {
        containerRef.current.innerHTML = e.data.html;
      }
    };
    worker.addEventListener("message", onMsg);

    const p = getPmdoc();
    if (p) worker.postMessage({ pmdoc: p });

    const unsub = subscribe((doc)=> {
      worker.postMessage({ pmdoc: doc });
    });

    return () => { unsub(); worker.removeEventListener("message", onMsg); worker.terminate(); };
  }, [getPmdoc, subscribe]);

  return <div id="wechat-preview" ref={containerRef} className="h-full overflow-auto p-4" />;
}
