'use client';

import React from 'react';
import { GitBranch, FileText, Clock } from 'lucide-react';

interface StatusBarProps {
  wordCount: number;
  currentBranch: string;
  lastSaved?: string;
}

export function StatusBar({ wordCount, currentBranch, lastSaved }: StatusBarProps) {
  return (
    <div className="h-6 bg-primary text-primary-foreground border-t border-border flex items-center justify-between px-4 text-xs">
      {/* Left Section */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-1">
          <GitBranch className="w-3 h-3" />
          <span>{currentBranch}</span>
        </div>
        
        <div className="flex items-center space-x-1">
          <FileText className="w-3 h-3" />
          <span>Markdown</span>
        </div>

        {lastSaved && (
          <div className="flex items-center space-x-1">
            <Clock className="w-3 h-3" />
            <span>已保存 {lastSaved}</span>
          </div>
        )}
      </div>

      {/* Right Section */}
      <div className="flex items-center space-x-4">
        <span>{wordCount} 字</span>
        <span>UTF-8</span>
        <span>LF</span>
        <span>第 1 行，第 1 列</span>
      </div>
    </div>
  );
}