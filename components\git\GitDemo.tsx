'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { SimpleGitService } from '@/lib/simpleGit';
import { GitConfigDialog } from './GitConfigDialog';
import { RepoConnectDialog } from './RepoConnectDialog';

export function GitDemo() {
  const [gitService] = useState(() => SimpleGitService.getInstance());
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [repoDialogOpen, setRepoDialogOpen] = useState(false);
  const [status, setStatus] = useState<string>('');

  const testGitFeatures = async () => {
    try {
      setStatus('测试Git功能...');
      
      // 测试写入文件
      await gitService.writeFile('test.md', '# 测试文档\n\n这是一个测试文档。');
      setStatus('✅ 文件写入成功');
      
      // 测试读取文件
      const content = await gitService.readFile('test.md');
      console.log('读取的文件内容:', content);
      setStatus('✅ 文件读取成功');
      
    } catch (error) {
      setStatus(`❌ 错误: ${error}`);
    }
  };

  return (
    <div className="p-4 space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Git工作流演示</CardTitle>
          <CardDescription>
            测试Git集成功能，包括配置、仓库连接和基本操作
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-2">
            <Button onClick={() => setConfigDialogOpen(true)}>
              Git配置
            </Button>
            <Button onClick={() => setRepoDialogOpen(true)}>
              连接仓库
            </Button>
            <Button onClick={testGitFeatures}>
              测试功能
            </Button>
          </div>
          
          {status && (
            <div className="p-3 bg-muted rounded-md">
              <p className="text-sm">{status}</p>
            </div>
          )}
        </CardContent>
      </Card>

      <GitConfigDialog
        open={configDialogOpen}
        onOpenChange={setConfigDialogOpen}
      />
      
      <RepoConnectDialog
        open={repoDialogOpen}
        onOpenChange={setRepoDialogOpen}
      />
    </div>
  );
}
