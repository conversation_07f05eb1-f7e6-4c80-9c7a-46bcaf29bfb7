export const TAGS: Record<string,{keep:boolean;attrs?:string[]}> = {
  article:{keep:true}, p:{keep:true},
  h1:{keep:true},h2:{keep:true},h3:{keep:true},
  strong:{keep:true},em:{keep:true},code:{keep:true},
  a:{keep:true,attrs:["href","title"]},
  ul:{keep:true},ol:{keep:true},li:{keep:true},
  blockquote:{keep:true}, pre:{keep:true},
  table:{keep:true}, thead:{keep:true}, tbody:{keep:true}, tr:{keep:true}, th:{keep:true}, td:{keep:true},
  img:{keep:true,attrs:["src","alt","width","height"]},
};
export const DROP_ATTRS = ["class","id","onclick","onerror","style"];
