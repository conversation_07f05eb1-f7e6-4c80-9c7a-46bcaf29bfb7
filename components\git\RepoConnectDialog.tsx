'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { SimpleGitService, RepoInfo } from '@/lib/simpleGit';

interface RepoConnectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onRepoConnected?: (repo: RepoInfo) => void;
}

export function RepoConnectDialog({ open, onOpenChange, onRepoConnected }: RepoConnectDialogProps) {
  const [repoInfo, setRepoInfo] = useState<RepoInfo>({
    owner: '',
    repo: '',
    branch: 'main'
  });
  const [loading, setLoading] = useState(false);
  const [mode, setMode] = useState<'clone' | 'init'>('clone');

  const handleClone = async () => {
    if (!repoInfo.owner.trim() || !repoInfo.repo.trim()) {
      alert('请填写仓库所有者和仓库名');
      return;
    }

    setLoading(true);
    try {
      const gitService = SimpleGitService.getInstance();

      await gitService.connectRepo(repoInfo);

      if (onRepoConnected) {
        onRepoConnected(repoInfo);
      }

      onOpenChange(false);
      alert('仓库连接成功！');
    } catch (error) {
      console.error('Connect failed:', error);
      alert(`连接仓库失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const handleInit = async () => {
    setLoading(true);
    try {
      const gitService = SimpleGitService.getInstance();
      await gitService.initRepo();

      const newRepo: RepoInfo = {
        owner: 'local',
        repo: 'new-repo',
        branch: 'main'
      };

      if (onRepoConnected) {
        onRepoConnected(newRepo);
      }

      onOpenChange(false);
      alert('仓库初始化成功！');
    } catch (error) {
      console.error('Init failed:', error);
      alert(`初始化仓库失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const parseGitHubUrl = (url: string) => {
    // 解析GitHub URL: https://github.com/owner/repo
    const match = url.match(/github\.com\/([^\/]+)\/([^\/]+)/);
    if (match) {
      setRepoInfo({
        owner: match[1],
        repo: match[2].replace('.git', ''),
        branch: repoInfo.branch
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>连接Git仓库</DialogTitle>
          <DialogDescription>
            克隆现有仓库或初始化新仓库
          </DialogDescription>
        </DialogHeader>
        
        <Tabs value={mode} onValueChange={(value) => setMode(value as 'clone' | 'init')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="clone">克隆仓库</TabsTrigger>
            <TabsTrigger value="init">新建仓库</TabsTrigger>
          </TabsList>
          
          <TabsContent value="clone" className="space-y-4">
            <div className="grid gap-4">
              <div>
                <Label htmlFor="github-url">GitHub仓库URL (可选)</Label>
                <Input
                  id="github-url"
                  placeholder="https://github.com/owner/repo"
                  onChange={(e) => parseGitHubUrl(e.target.value)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  粘贴GitHub仓库URL自动填充下方信息
                </p>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="owner">仓库所有者</Label>
                  <Input
                    id="owner"
                    value={repoInfo.owner}
                    onChange={(e) => setRepoInfo({ ...repoInfo, owner: e.target.value })}
                    placeholder="username"
                  />
                </div>
                <div>
                  <Label htmlFor="repo">仓库名</Label>
                  <Input
                    id="repo"
                    value={repoInfo.repo}
                    onChange={(e) => setRepoInfo({ ...repoInfo, repo: e.target.value })}
                    placeholder="repository"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="branch">分支名</Label>
                <Input
                  id="branch"
                  value={repoInfo.branch}
                  onChange={(e) => setRepoInfo({ ...repoInfo, branch: e.target.value })}
                  placeholder="main"
                />
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="init" className="space-y-4">
            <div className="text-center py-8">
              <h3 className="text-lg font-medium">初始化新仓库</h3>
              <p className="text-muted-foreground mt-2">
                在本地创建一个新的Git仓库，稍后可以连接到GitHub
              </p>
            </div>
          </TabsContent>
        </Tabs>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          {mode === 'clone' ? (
            <Button onClick={handleClone} disabled={loading}>
              {loading ? '克隆中...' : '克隆仓库'}
            </Button>
          ) : (
            <Button onClick={handleInit} disabled={loading}>
              {loading ? '初始化中...' : '初始化仓库'}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
