// GitHub API服务类
export interface GitHubRepo {
  id: number;
  name: string;
  full_name: string;
  description: string | null;
  private: boolean;
  html_url: string;
  clone_url: string;
  default_branch: string;
  updated_at: string;
  owner: {
    login: string;
    avatar_url: string;
  };
}

export interface GitHubUser {
  id: number;
  login: string;
  name: string | null;
  email: string | null;
  avatar_url: string;
  bio: string | null;
  public_repos: number;
  followers: number;
  following: number;
}

export interface GitHubFile {
  name: string;
  path: string;
  sha: string;
  size: number;
  url: string;
  html_url: string;
  git_url: string;
  download_url: string | null;
  type: 'file' | 'dir';
  content?: string;
  encoding?: string;
}

export interface GitHubCommit {
  sha: string;
  node_id: string;
  commit: {
    author: {
      name: string;
      email: string;
      date: string;
    };
    committer: {
      name: string;
      email: string;
      date: string;
    };
    message: string;
    tree: {
      sha: string;
      url: string;
    };
    url: string;
    comment_count: number;
  };
  url: string;
  html_url: string;
  comments_url: string;
  author: {
    login: string;
    id: number;
    avatar_url: string;
    gravatar_id: string | null;
    url: string;
    html_url: string;
    type: string;
    site_admin: boolean;
  } | null;
  committer: {
    login: string;
    id: number;
    avatar_url: string;
    gravatar_id: string | null;
    url: string;
    html_url: string;
    type: string;
    site_admin: boolean;
  } | null;
  parents: Array<{
    sha: string;
    url: string;
    html_url: string;
  }>;
}

export class GitHubService {
  private accessToken: string;
  private baseUrl = 'https://api.github.com';

  constructor(accessToken: string) {
    this.accessToken = accessToken;
  }

  // 创建请求头
  private getHeaders(): HeadersInit {
    return {
      'Authorization': `Bearer ${this.accessToken}`,
      'Accept': 'application/vnd.github.v3+json',
      'Content-Type': 'application/json'
    };
  }

  // 获取用户信息
  async getUser(): Promise<GitHubUser> {
    const response = await fetch(`${this.baseUrl}/user`, {
      headers: this.getHeaders()
    });

    if (!response.ok) {
      throw new Error(`获取用户信息失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 获取用户仓库列表
  async getUserRepos(page = 1, per_page = 30): Promise<GitHubRepo[]> {
    const response = await fetch(
      `${this.baseUrl}/user/repos?page=${page}&per_page=${per_page}&sort=updated`,
      {
        headers: this.getHeaders()
      }
    );

    if (!response.ok) {
      throw new Error(`获取仓库列表失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 获取所有用户仓库（分页获取）
  async getAllUserRepos(): Promise<GitHubRepo[]> {
    const allRepos: GitHubRepo[] = [];
    let page = 1;
    const per_page = 100; // 每页最多100个

    while (true) {
      const repos = await this.getUserRepos(page, per_page);
      allRepos.push(...repos);

      // 如果返回的仓库数量少于per_page，说明已经是最后一页
      if (repos.length < per_page) {
        break;
      }

      page++;

      // 安全限制：最多获取1000个仓库（10页）
      if (page > 10) {
        console.warn('仓库数量过多，已限制为1000个');
        break;
      }
    }

    return allRepos;
  }

  // 获取仓库信息
  async getRepo(owner: string, repo: string): Promise<GitHubRepo> {
    const response = await fetch(`${this.baseUrl}/repos/${owner}/${repo}`, {
      headers: this.getHeaders()
    });

    if (!response.ok) {
      throw new Error(`获取仓库信息失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 获取仓库文件列表
  async getRepoContents(owner: string, repo: string, path = ''): Promise<GitHubFile[] | GitHubFile> {
    const response = await fetch(
      `${this.baseUrl}/repos/${owner}/${repo}/contents/${path}`,
      {
        headers: this.getHeaders()
      }
    );

    if (!response.ok) {
      throw new Error(`获取文件列表失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 获取文件内容
  async getFileContent(owner: string, repo: string, path: string): Promise<string> {
    const response = await fetch(
      `${this.baseUrl}/repos/${owner}/${repo}/contents/${path}`,
      {
        headers: this.getHeaders()
      }
    );

    if (!response.ok) {
      throw new Error(`获取文件内容失败: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.type !== 'file') {
      throw new Error('指定路径不是文件');
    }

    // 解码Base64内容，正确处理UTF-8编码
    const base64Content = data.content.replace(/\n/g, '');

    // 使用TextDecoder正确解码UTF-8字符
    try {
      // 先将Base64解码为字节数组
      const binaryString = atob(base64Content);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // 使用TextDecoder将字节数组解码为UTF-8字符串
      const decoder = new TextDecoder('utf-8');
      return decoder.decode(bytes);
    } catch (error) {
      // 如果UTF-8解码失败，尝试直接使用atob（用于纯ASCII内容）
      console.warn('UTF-8解码失败，使用ASCII解码:', error);
      return atob(base64Content);
    }
  }

  // 创建或更新文件
  async createOrUpdateFile(
    owner: string,
    repo: string,
    path: string,
    content: string,
    message: string,
    sha?: string
  ): Promise<any> {
    console.log(`GitHub API: 创建/更新文件 ${owner}/${repo}/${path}`);
    console.log(`内容长度: ${content.length}, 有SHA: ${!!sha}`);

    const body: any = {
      message,
      content: btoa(unescape(encodeURIComponent(content))) // 编码为Base64
    };

    if (sha) {
      body.sha = sha;
    }

    console.log('请求体:', { ...body, content: `[Base64内容,长度:${body.content.length}]` });

    const url = `${this.baseUrl}/repos/${owner}/${repo}/contents/${path}`;
    console.log('请求URL:', url);

    const response = await fetch(url, {
      method: 'PUT',
      headers: this.getHeaders(),
      body: JSON.stringify(body)
    });

    console.log('响应状态:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('GitHub API错误响应:', errorText);
      throw new Error(`文件操作失败 (${response.status}): ${response.statusText}\n${errorText}`);
    }

    const result = await response.json();
    console.log('GitHub API成功响应:', result);
    return result;
  }

  // 删除文件
  async deleteFile(
    owner: string,
    repo: string,
    path: string,
    message: string,
    sha: string
  ): Promise<any> {
    const response = await fetch(
      `${this.baseUrl}/repos/${owner}/${repo}/contents/${path}`,
      {
        method: 'DELETE',
        headers: this.getHeaders(),
        body: JSON.stringify({
          message,
          sha
        })
      }
    );

    if (!response.ok) {
      throw new Error(`删除文件失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 创建仓库
  async createRepo(name: string, description?: string, isPrivate = false): Promise<GitHubRepo> {
    const response = await fetch(`${this.baseUrl}/user/repos`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify({
        name,
        description,
        private: isPrivate,
        auto_init: true
      })
    });

    if (!response.ok) {
      throw new Error(`创建仓库失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 获取提交历史
  async getCommits(owner: string, repo: string, page = 1, per_page = 30, sha?: string): Promise<GitHubCommit[]> {
    let url = `${this.baseUrl}/repos/${owner}/${repo}/commits?page=${page}&per_page=${per_page}`;
    if (sha) {
      url += `&sha=${sha}`;
    }

    const response = await fetch(url, {
      headers: this.getHeaders()
    });

    if (!response.ok) {
      throw new Error(`获取提交历史失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 获取特定提交的详细信息
  async getCommit(owner: string, repo: string, sha: string): Promise<GitHubCommit & { stats?: { total: number; additions: number; deletions: number }; files?: any[] }> {
    const response = await fetch(
      `${this.baseUrl}/repos/${owner}/${repo}/commits/${sha}`,
      {
        headers: this.getHeaders()
      }
    );

    if (!response.ok) {
      throw new Error(`获取提交详情失败: ${response.statusText}`);
    }

    return response.json();
  }

  // 获取特定提交时的文件内容
  async getFileContentAtCommit(owner: string, repo: string, path: string, sha: string): Promise<string> {
    const response = await fetch(
      `${this.baseUrl}/repos/${owner}/${repo}/contents/${path}?ref=${sha}`,
      {
        headers: this.getHeaders()
      }
    );

    if (!response.ok) {
      throw new Error(`获取历史文件内容失败: ${response.statusText}`);
    }

    const data = await response.json();

    if (data.type !== 'file') {
      throw new Error('指定路径不是文件');
    }

    // 解码Base64内容，正确处理UTF-8编码
    const base64Content = data.content.replace(/\n/g, '');

    // 使用TextDecoder正确解码UTF-8字符
    try {
      // 先将Base64解码为字节数组
      const binaryString = atob(base64Content);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // 使用TextDecoder将字节数组解码为UTF-8字符串
      const decoder = new TextDecoder('utf-8');
      return decoder.decode(bytes);
    } catch (error) {
      // 如果UTF-8解码失败，尝试直接使用atob（用于纯ASCII内容）
      console.warn('UTF-8解码失败，使用ASCII解码:', error);
      return atob(base64Content);
    }
  }

  // 获取仓库分支列表
  async getBranches(owner: string, repo: string): Promise<{ name: string; commit: { sha: string; url: string } }[]> {
    const response = await fetch(
      `${this.baseUrl}/repos/${owner}/${repo}/branches`,
      {
        headers: this.getHeaders()
      }
    );

    if (!response.ok) {
      throw new Error(`获取分支列表失败: ${response.statusText}`);
    }

    return response.json();
  }
}
