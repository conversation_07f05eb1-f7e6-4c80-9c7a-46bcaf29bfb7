'use client';

import * as React from 'react';
import { useState } from 'react';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { FileText, Folder, Info, Lightbulb } from 'lucide-react';

interface CreateFileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (fileName: string) => void;
  initialPath?: string;
  type: 'file' | 'folder';
}

export function CreateFileDialog({
  open,
  onOpenChange,
  onConfirm,
  initialPath = '',
  type = 'file'
}: CreateFileDialogProps) {
  const [fileName, setFileName] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // 验证输入
    if (!fileName.trim()) {
      setError(`请输入${type === 'folder' ? '文件夹' : '文件'}名称`);
      return;
    }

    // 验证文件名格式
    const invalidChars = /[<>:"/\\|?*]/;
    if (invalidChars.test(fileName)) {
      setError('文件名不能包含以下字符: < > : " / \\ | ? *');
      return;
    }

    // 验证文件扩展名（仅对文件）
    if (type === 'file' && !fileName.includes('.')) {
      setError('请为文件添加扩展名，例如: .md, .txt');
      return;
    }

    onConfirm(fileName.trim());
    handleClose();
  };

  const handleClose = () => {
    setFileName('');
    setError('');
    onOpenChange(false);
  };

  const getIcon = () => {
    return type === 'folder' ? <Folder className="w-5 h-5 text-blue-500" /> : <FileText className="w-5 h-5 text-green-500" />;
  };

  const getTitle = () => {
    if (type === 'folder') {
      return initialPath ? `在 "${initialPath}" 中新建文件夹` : '新建文件夹';
    }
    return initialPath ? `在 "${initialPath}" 中新建文件` : '新建文件';
  };

  const getPlaceholder = () => {
    return type === 'folder' ? '输入文件夹名称...' : '输入文件名（如: document.md）...';
  };

  const getExamples = () => {
    if (type === 'folder') {
      return [
        { name: 'docs', desc: '文档文件夹' },
        { name: 'images', desc: '图片文件夹' },
        { name: 'my-project', desc: '项目文件夹' }
      ];
    }
    return [
      { name: 'README.md', desc: 'Markdown文档' },
      { name: 'notes.txt', desc: '文本文件' },
      { name: 'config.json', desc: 'JSON配置文件' }
    ];
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {getIcon()}
            {getTitle()}
          </DialogTitle>
          <DialogDescription>
            {type === 'folder' 
              ? '创建一个新的文件夹来组织您的文件'
              : '创建一个新的文件进行编辑'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="fileName" className="text-sm font-medium">
              {type === 'folder' ? '文件夹名称' : '文件名称'}
            </Label>
            <Input
              id="fileName"
              value={fileName}
              onChange={(e) => {
                setFileName(e.target.value);
                setError(''); // 清除错误信息
              }}
              placeholder={getPlaceholder()}
              className="w-full"
              autoFocus
            />
            {error && (
              <Alert className="border-destructive bg-destructive/10">
                <Info className="h-4 w-4 text-destructive" />
                <AlertDescription className="text-destructive text-sm">
                  {error}
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* 使用示例 */}
          <div className="bg-muted/50 rounded-lg p-3 space-y-2">
            <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
              <Lightbulb className="w-4 h-4" />
              示例
            </div>
            <div className="space-y-1">
              {getExamples().map((example, index) => (
                <div key={index} className="flex items-center justify-between text-xs">
                  <code className="bg-background px-2 py-1 rounded text-foreground font-mono">
                    {example.name}
                  </code>
                  <span className="text-muted-foreground">{example.desc}</span>
                </div>
              ))}
            </div>
          </div>

          {/* 说明文字 */}
          <div className="text-xs text-muted-foreground space-y-1">
            {type === 'folder' ? (
              <>
                <div>• 文件夹名称不能包含特殊字符</div>
                <div>• 可以使用中文、英文、数字和连字符</div>
              </>
            ) : (
              <>
                <div>• 支持创建多级目录：<code className="bg-muted px-1 rounded">folder/file.md</code></div>
                <div>• 推荐使用 .md 扩展名创建Markdown文件</div>
                <div>• 也支持 .txt、.json 等其他格式</div>
              </>
            )}
          </div>

          <DialogFooter className="gap-2">
            <Button type="button" variant="outline" onClick={handleClose}>
              取消
            </Button>
            <Button type="submit" className="min-w-20">
              创建{type === 'folder' ? '文件夹' : '文件'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
} 