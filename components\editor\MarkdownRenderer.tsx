'use client';

import React, { useMemo, useRef, useEffect } from 'react';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkGfm from 'remark-gfm';
import remarkRehype from 'remark-rehype';
import rehypeHighlight from 'rehype-highlight';
import rehypeStringify from 'rehype-stringify';
import rehypeRaw from 'rehype-raw';

interface MarkdownRendererProps {
  content: string;
  className?: string;
  initialScrollPosition?: number;
  onScrollPositionChange?: (position: number) => void;
}

interface MarkdownSegment {
  id: string;
  content: string;
  html: string;
  type: 'paragraph' | 'heading' | 'code' | 'list' | 'other';
}

// 渲染单个Markdown段落为HTML
const renderMarkdownSegment = (markdown: string) => {
  try {
    const processor = unified()
      .use(remarkParse)
      .use(remarkGfm)
      .use(remarkRehype, { allowDangerousHtml: true })
      .use(rehypeRaw)
      .use(rehypeHighlight)
      .use(rehypeStringify);

    let result = processor.processSync(markdown);
    let htmlString = String(result);
    
    // 为标题添加ID
    htmlString = htmlString.replace(
      /<(h[1-6])>([^<]+)<\/h[1-6]>/g,
      (match, tag, text) => {
        const id = text.toLowerCase()
          .replace(/[^\w\u4e00-\u9fa5\s-]/g, '')
          .replace(/\s+/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '');
        
        if (id) {
          return `<${tag} id="${id}">${text}</${tag}>`;
        }
        return match;
      }
    );
    
    return htmlString;
  } catch (error) {
    console.error('Markdown segment rendering error:', error);
    return '<p>渲染错误</p>';
  }
};

// 判断段落类型
const getSegmentType = (content: string): 'paragraph' | 'heading' | 'code' | 'list' | 'other' => {
  const trimmed = content.trim();
  if (trimmed.startsWith('#')) return 'heading';
  if (trimmed.startsWith('```')) return 'code';
  if (trimmed.startsWith('- ') || trimmed.startsWith('* ') || /^\d+\.\s/.test(trimmed)) return 'list';
  if (trimmed.length > 0) return 'paragraph';
  return 'other';
};

export default function MarkdownRenderer({
  content,
  className = '',
  initialScrollPosition,
  onScrollPositionChange
}: MarkdownRendererProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  // 监听跳转到预览标题的事件
  useEffect(() => {
    const handleScrollToPreviewHeading = (event: CustomEvent<{ headingId: string }>) => {
      if (containerRef.current) {
        const headingElement = containerRef.current.querySelector(`#${event.detail.headingId}`);
        if (headingElement) {
          headingElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }
    };

    document.addEventListener('scrollToPreviewHeading', handleScrollToPreviewHeading as EventListener);
    
    return () => {
      document.removeEventListener('scrollToPreviewHeading', handleScrollToPreviewHeading as EventListener);
    };
  }, []);

  // 将内容分割成段落进行增量渲染
  const segments = useMemo((): MarkdownSegment[] => {
    if (!content.trim()) {
      return [];
    }

    const lines = content.split('\n');
    const segments: MarkdownSegment[] = [];
    let currentSegment = '';
    let segmentIndex = 0;
    let inCodeBlock = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // 检测代码块
      if (line.trim().startsWith('```')) {
        inCodeBlock = !inCodeBlock;
        currentSegment += (currentSegment ? '\n' : '') + line;
        
        // 代码块结束时，立即创建段落
        if (!inCodeBlock && currentSegment.trim()) {
          segments.push({
            id: `segment-${segmentIndex}`,
            content: currentSegment.trim(),
            html: renderMarkdownSegment(currentSegment.trim()),
            type: getSegmentType(currentSegment.trim())
          });
          segmentIndex++;
          currentSegment = '';
        }
        continue;
      }

      // 在代码块内，直接添加行
      if (inCodeBlock) {
        currentSegment += (currentSegment ? '\n' : '') + line;
        continue;
      }

      // 代码块外的处理
      if (line.trim() === '') {
        // 空行：结束当前段落
        if (currentSegment.trim() !== '') {
          segments.push({
            id: `segment-${segmentIndex}`,
            content: currentSegment.trim(),
            html: renderMarkdownSegment(currentSegment.trim()),
            type: getSegmentType(currentSegment.trim())
          });
          segmentIndex++;
        }
        currentSegment = '';
      } else {
        // 非空行：添加到当前段落
        currentSegment += (currentSegment ? '\n' : '') + line;
      }
    }

    // 处理最后一个段落
    if (currentSegment.trim() !== '') {
      segments.push({
        id: `segment-${segmentIndex}`,
        content: currentSegment.trim(),
        html: renderMarkdownSegment(currentSegment.trim()),
        type: getSegmentType(currentSegment.trim())
      });
    }

    return segments;
  }, [content]);

  // 恢复滚动位置（只在初始化时执行一次）
  useEffect(() => {
    if (containerRef.current && initialScrollPosition !== undefined) {
      // 使用 requestAnimationFrame 确保DOM已经渲染完成
      requestAnimationFrame(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = initialScrollPosition;
        }
      });
    }
  }, [initialScrollPosition]);

  // 监听滚动事件
  useEffect(() => {
    const container = containerRef.current;
    if (!container || !onScrollPositionChange) return;

    const handleScroll = () => {
      onScrollPositionChange(container.scrollTop);
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [onScrollPositionChange]);

  return (
    <div
      ref={containerRef}
      className={`prose prose-slate dark:prose-invert max-w-none p-4 overflow-auto h-full ${className}`}
      style={{
        lineHeight: '1.7',
        fontSize: '16px'
      }}
    >
      {segments.map((segment) => (
        <MarkdownSegmentRenderer 
          key={segment.id} 
          segment={segment} 
        />
      ))}
      
      {segments.length === 0 && (
        <div className="text-muted-foreground text-center py-8">
          暂无内容
        </div>
      )}
    </div>
  );
}

// 段落渲染组件
interface MarkdownSegmentRendererProps {
  segment: MarkdownSegment;
}

function MarkdownSegmentRenderer({ segment }: MarkdownSegmentRendererProps) {
  return (
    <div 
      className="markdown-segment"
      data-segment-id={segment.id}
      data-segment-type={segment.type}
      dangerouslySetInnerHTML={{ __html: segment.html }}
    />
  );
}
